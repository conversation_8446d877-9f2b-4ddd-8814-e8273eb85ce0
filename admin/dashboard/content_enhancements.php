<!DOCTYPE html>
<html lang="en" class="root-text-sm">
    <head>
        <title>Product Content | Grow ECommerce Inc.</title>
        <?php include ('../includes/header.php'); ?>
				<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
				<style>
      .custom-line {
            border: none;
            border-top: 1px solid #FAFAFAD6; /* Change the width and color as needed */
            width: 100%;
            margin: 0; /* Remove margins to ensure zero padding around the line */
            padding: 0; /* Ensure no padding */
        }
				.custom-line2 {
            border: none;
            border-top: 1px solid #FAFAFA57; /* Change the width and color as needed */
            width: 100%;
            margin: 0; /* Remove margins to ensure zero padding around the line */
            padding: 0; /* Ensure no padding */
        }
				.tooltip-inner {
      text-align: left !important;
  }
				</style>

    </head>
    <!-- BEGIN Body -->
    <body class="<?=$htmlclass?>">
        <!-- DOC: script to save and load page settings -->
        <?php include ('../includes/page_settings_save.php'); ?>
        <!-- BEGIN Page Wrapper -->
        <div class="page-wrapper">
            <div class="page-inner">
                <!-- BEGIN Left Aside -->
                <?php include ('../includes/sidebar.php'); ?>
                <!-- END Left Aside -->
                <div class="page-content-wrapper">
                    <!-- BEGIN Page Header -->
                    <?php include ('../includes/navbar.php'); ?>
                    <!-- END Page Header -->
                    <!-- BEGIN Page Content -->
                    <!-- the #js-page-content id is needed for some plugins to initialize -->
                    <main id="js-page-content" role="main" class="page-content">
                    <?php
								$_SESSION['all_clients'] ="";

								$prefix = "";
								
								$prefix =	$_SESSION['global_clientPrefix'];
								if($prefix == "all_clients"){
									$_SESSION['all_clients'] = $prefix;
									$clause = "client_prefix IS NULL ";
								}else{
									$_SESSION['all_clients'] = '';
									$clause = "client_prefix = '$prefix' ";
								}
								?>
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);">Dashboard</a></li>
                            <li class="breadcrumb-item active">Retailers</li>
														<li class=" "> 
															<!-- <code class="badge badge-info p-1 ml-4">
															<a href="javascript:void(0);" class="fas fa-undo text-light" id="refresh"> <i>Refresh</i></a> 
															</code>  -->
														</li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block">
                                <span class="js-get-date"></span>
																
                            </li>
														<div class="col-md-12" style="margin-top: -1.5%;">
															<div class="row overflow-hidden">
																<div class="col-md-4"></div>
																<div class="col-md-4 d-flex justify-content-center">
																	<div class="col-md-6">
																		<select name="" id="select_stock" class="select2 form-control form-control-sm select2-hidden-accessible" data-select2-id="single-default" tabindex="-1" aria-hidden="true">
																		<option value="InStock" selected>In Stock</option>
																		<option value="">All</option>
																		</select>
																	</div>
																</div>
																<div class="col-md-4"></div>
															</div>
														</div>
                        </ol>

                        <div class="row">
                            <div class="col-xl-12">
																<div class="row p-2">

																	<div class="col-md-3 mt-3">
																		<div class="card shadow rounded-lg border-0" style="background-color: #f0f3f5;">
																			<div class="card-header text-center font-weight-bold text-dark bg-light border-bottom py-2" style="font-size: 1.1rem;">
																				<i class="fal fa-info-circle text-info float-left" data-toggle="tooltip" data-placement="right" data-html="true"  data-original-title="GEC Managed = YES <br>Item Status = Active,PDisc,Future <br>Item Type = Product,Set <br>(Item Type = SKU or SKU Type = White Label)"></i> Product Titles
																				<span id="count_category_specific_percent" class="float-right mr-3"></span>
																			</div>
																			<div class="card-body py-3 px-2">
																				<div class="d-flex text-center font-weight-bold mb-2" style="font-size: 0.85rem;">
																					<div class="col-sm-4"></div>
																					<div class="col-sm-4 text-danger">Missing</div>
																					<div class="col-sm-4 " style="color: black;">Web Ready</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Single Product title</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gecsingleproducttitlemissing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Single Product Title'>
																							<span id="count_gec_single_product_title_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_single_product_title_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Variation Product title</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gecvariationproducttitlemissing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Variation Product Title'>
																							<span id="count_gec_variation_product_title_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_variation_product_title_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<p class="text-secondary text-center">GEC & GEC RETAILER SINGLE PRODUCT TITLES</p>

																				<!-- Dynamic Retailer Sections - Single Product Titles -->
																				<div class="retailer-section" data-retailer="thd" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Home Depot Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_home_depot_single_product_title_missing&select2=gec_retailer_single_product_title&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Home Depot Single Product Title'>
																								<span id="count_gec_home_depot_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_home_depot_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="bbbovsk" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC BBB/Overstock Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_bbb_overstock_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC BBB/Overstock Single Product Title'>
																								<span id="count_gec_bbb_overstock_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_bbb_overstock_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="trgt" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Target Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_target_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Target Single Product Title'>
																								<span id="count_gec_target_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_target_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wmrl" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Walmart Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_walmart_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Walmart Single Product Title'>
																								<span id="count_gec_walmart_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_walmart_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="amzv" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Amazon Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_amazon_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Amazon Single Product Title'>
																								<span id="count_gec_amazon_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_amazon_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wayf" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Wayfair Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_wayfair_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Wayfair Single Product Title'>
																								<span id="count_gec_wayfair_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_wayfair_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="houz" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Houzz Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_houzz_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Houzz Single Product Title'>
																								<span id="count_gec_houzz_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_houzz_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="thdc" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Home Depot CA Single Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" onclick="window.open('../content/product_titles.php?select1=gec_home_depot_ca_single_product_title_missing&instock=' + encodeURIComponent($('#select_stock').val()), '_blank')" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Home Depot CA Single Product Title'>
																								<span id="count_gec_home_depot_ca_single_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_home_depot_ca_single_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<p class="text-secondary text-center">GEC & GEC RETAILER VARIATION PRODUCT TITLES</p>

																				<!-- Dynamic Retailer Sections - Variation Product Titles -->
																				<div class="retailer-section" data-retailer="thd" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Home Depot Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Home Depot Variation Product Title'>
																								<span id="count_gec_home_depot_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_home_depot_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="bbbovsk" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC BBB/Overstock Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC BBB/Overstock Variation Product Title'>
																								<span id="count_gec_bbb_overstock_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_bbb_overstock_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="trgt" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Target Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Target Variation Product Title'>
																								<span id="count_gec_target_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_target_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wmrl" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Walmart Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Walmart Variation Product Title'>
																								<span id="count_gec_walmart_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_walmart_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="amzv" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Amazon Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Amazon Variation Product Title'>
																								<span id="count_gec_amazon_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_amazon_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wayf" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Wayfair Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Wayfair Variation Product Title'>
																								<span id="count_gec_wayfair_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_wayfair_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="houz" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Houzz Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Houzz Variation Product Title'>
																								<span id="count_gec_houzz_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_houzz_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="thdc" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Home Depot CA Variation Product Title</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Home Depot CA Variation Product Title'>
																								<span id="count_gec_home_depot_ca_variation_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_gec_home_depot_ca_variation_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<p class="text-secondary text-center">CLIENT & LIVE RETAILER PRODUCT TITLES</p>

																				<!-- Dynamic Retailer Sections - Live Product Titles -->
																				<div class="retailer-section" data-retailer="thd" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Home Depot Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Home Depot Product Title'>
																								<span id="count_live_home_depot_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_home_depot_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="bbbovsk" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live BBB/Overstock Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live BBB/Overstock Product Title'>
																								<span id="count_live_bbb_overstock_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_bbb_overstock_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="trgt" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Target Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Target Product Title'>
																								<span id="count_live_target_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_target_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wmrl" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Walmart Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Walmart Product Title'>
																								<span id="count_live_walmart_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_walmart_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="amzv" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Amazon VC Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Amazon VC Product Title'>
																								<span id="count_live_amazon_vc_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_amazon_vc_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="wayf" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Wayfair Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Wayfair Product Title'>
																								<span id="count_live_wayfair_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_wayfair_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="amzs" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Amazon SC Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Amazon SC Product Title'>
																								<span id="count_live_amazon_sc_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_amazon_sc_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="houz" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Houzz Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Houzz Product Title'>
																								<span id="count_live_houzz_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_houzz_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																					<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				</div>

																				<div class="retailer-section" data-retailer="thdc" style="display: none;">
																					<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																						<div class="col-sm-4 text-left ml-2" style="color: black;">Live Home Depot CA Product Title </div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='Live Home Depot CA Product Title'>
																								<span id="count_live_home_depot_ca_product_title_missing"></span>
																							</a>
																						</div>
																						<div class="col-sm-4 text-center">
																							<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																								<span id="count_live_home_depot_ca_product_title_webready"></span>
																							</a>
																						</div>
																					</div>
																				</div>

																			</div>
																		</div>
																	</div>

																	<div class="col-md-3 mt-3">
																		<div class="card shadow rounded-lg border-0" style="background-color: #f0f3f5;">
																			<div class="card-header text-center font-weight-bold text-dark bg-light border-bottom py-2" style="font-size: 1.1rem;">
																				<i class="fal fa-info-circle text-info float-left" data-toggle="tooltip" data-placement="right" data-html="true"  data-original-title="GEC Managed = YES <br>Item Status = Active,PDisc <br>Item Type = Product,Set <br>(Item Type = SKU or SKU Type = White Label)"></i> Descriptions
																				<span id="count__percent" class="float-right mr-3"></span>
																			</div>
																			<div class="card-body py-3 px-2">
																				<div class="d-flex text-center font-weight-bold mb-2" style="font-size: 0.9rem;">
																					<div class="col-sm-4"></div>
																					<div class="col-sm-4 text-danger">Missing</div>
																					<div class="col-sm-4 " style="color: black;">Web Ready</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.9rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Variation Description</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Variation Description'>
																							<span id="count_variation_description_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_variation_description_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.9rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Single Item Description</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Single Item Description'>
																							<span id="count_single_item_description_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_single_item_description_webready"></span>
																						</a>
																					</div>
																				</div>

																			</div>
																		</div>
																	</div>

																	<div class="col-md-3 mt-3">
																		<div class="card shadow rounded-lg border-0" style="background-color: #f0f3f5;">
																			<div class="card-header text-center font-weight-bold text-dark bg-light border-bottom py-2" style="font-size: 1.1rem;">
																				<i class="fal fa-info-circle text-info float-left" data-toggle="tooltip" data-placement="right" data-html="true"  data-original-title="GEC Managed = YES <br>Item Status = Active,PDisc <br>Item Type = Product,Set <br>(Item Type = SKU or SKU Type = White Label)"></i> Bullet Points
																				<span id="count__percent" class="float-right mr-3"></span>
																			</div>
																			<div class="card-body py-3 px-2">
																				<div class="d-flex text-center font-weight-bold mb-2" style="font-size: 0.85rem;">
																					<div class="col-sm-4"></div>
																					<div class="col-sm-4 text-danger">Missing</div>
																					<div class="col-sm-4 " style="color: black;">Web Ready</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 1</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 1'>
																							<span id="count_gec_bullet_point_1_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_1_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 2</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 2'>
																							<span id="count_gec_bullet_point_2_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_2_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 3</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 3'>
																							<span id="count_gec_bullet_point_3_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_3_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 4</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 4'>
																							<span id="count_gec_bullet_point_4_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_4_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 5</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 5'>
																							<span id="count_gec_bullet_point_5_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_5_webready"></span>
																						</a>
																					</div>
																				</div>

																				<!-- More/Less Button -->
																				<div class="text-center my-2">
																					<button id="toggleBulletPointsBtn" class="btn btn-sm btn-outline-primary" style="font-size: 0.75rem; padding: 2px 12px;">
																						<i class="fas fa-chevron-down mr-1"></i> More
																					</button>
																				</div>

																				<!-- Hidden Bullet Points Section -->
																				<div id="additionalBulletPoints" style="display: none;">
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 6</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 6'>
																							<span id="count_gec_bullet_point_6_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_6_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 7</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 7'>
																							<span id="count_gec_bullet_point_7_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_7_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 8</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 8'>
																							<span id="count_gec_bullet_point_8_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_8_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 9</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 9'>
																							<span id="count_gec_bullet_point_9_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_9_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 10</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 10'>
																							<span id="count_gec_bullet_point_10_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_10_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 11</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 11'>
																							<span id="count_gec_bullet_point_11_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_11_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 12</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 12'>
																							<span id="count_gec_bullet_point_12_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_12_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 13</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 13'>
																							<span id="count_gec_bullet_point_13_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_13_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 14</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 14'>
																							<span id="count_gec_bullet_point_14_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_14_webready"></span>
																						</a>
																					</div>
																				</div>
																				<hr class="my-2" style="border-top: 1px dashed #ccc;">
																				<div class="d-flex align-items-center" style="font-size: 0.8rem;">
																					<div class="col-sm-4 text-left ml-2" style="color: black;">GEC Bullet Point 15</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" class="text-danger" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title='GEC Bullet Point 15'>
																							<span id="count_gec_bullet_point_15_missing"></span>
																						</a>
																					</div>
																					<div class="col-sm-4 text-center">
																						<a href="javascript:void(0);" style="color: black;" data-toggle="tooltip" data-placement="right" data-html="true" data-original-title=''>
																							<span id="count_gec_bullet_point_15_webready"></span>
																						</a>
																					</div>
																				</div>
																				
																				</div>
																				<!-- End Hidden Bullet Points Section -->

																			</div>
																		</div>
																	</div>

																</div>
                            </div>
                        </div>
                    </main>
                    <!-- this overlay is activated only when mobile menu is triggered -->
                    <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div>
                    <!-- END Page Content -->
                    <!-- BEGIN Page Footer -->
                    <?php include ('../includes/footer.php'); ?>
                    <!-- END Page Footer -->
                    <!-- BEGIN Shortcuts -->
                    <?php include ('../includes/shortcuts.php'); ?>
                    <!-- END Shortcuts -->
                    <!-- BEGIN Color profile -->
                    <?php include ('../includes/color_profile.php'); ?>
                    <!-- END Color profile -->
                </div>
            </div>
        </div>
        <!-- END Page Wrapper -->
        <!-- BEGIN Quick Menu -->
        <!-- to add more items, please make sure to change the variable '$menu-items: number;' in your _page-components-shortcut.scss -->
        <?php include ('../includes/quick_menu.php'); ?>
        <!-- END Quick Menu -->
        <!-- BEGIN Messenger -->
        <?php include ('../includes/messenger.php'); ?>
        <!-- END Messenger -->
        <!-- BEGIN Page Settings -->
        <?php include ('../includes/page_settings.php'); ?>
        <!-- END Page Settings -->
        <?php include('../includes/bottom.php');?>

    </body>
    <!-- END Body -->
</html>

<script>
	 $(document).ready(function () {

		// Initialize client retailer permissions on page load
		initializeClientRetailerPermissions();

		//SHOT CALL

					//product titles
					count_gec_single_product_title_missing();
					count_gec_single_product_title_webready();
					count_gec_variation_product_title_missing();
					count_gec_variation_product_title_webready();
					//GEC &  GEC RETAILER SINGLE PRODUCT TITLES
					count_gec_home_depot_single_product_title_missing();
					count_gec_home_depot_single_product_title_webready();
					count_gec_bbb_overstock_single_product_title_missing();
					count_gec_bbb_overstock_single_product_title_webready();
					count_gec_target_single_product_title_missing();
					count_gec_target_single_product_title_webready();
					count_gec_walmart_single_product_title_missing();
					count_gec_walmart_single_product_title_webready();
					count_gec_amazon_single_product_title_missing();
					count_gec_amazon_single_product_title_webready();
					count_gec_wayfair_single_product_title_missing();
					count_gec_wayfair_single_product_title_webready();
					count_gec_houzz_single_product_title_missing();
					count_gec_houzz_single_product_title_webready();
					count_gec_home_depot_ca_single_product_title_missing();
					count_gec_home_depot_ca_single_product_title_webready();
					//GEC & GEC RETAILER VARIATION PRODUCT TITLES
					count_gec_home_depot_variation_product_title_missing();
					count_gec_home_depot_variation_product_title_webready();
					count_gec_bbb_overstock_variation_product_title_missing();
					count_gec_bbb_overstock_variation_product_title_webready();
					count_gec_target_variation_product_title_missing();
					count_gec_target_variation_product_title_webready();
					count_gec_walmart_variation_product_title_missing();
					count_gec_walmart_variation_product_title_webready();
					count_gec_amazon_variation_product_title_missing();
					count_gec_amazon_variation_product_title_webready();
					count_gec_wayfair_variation_product_title_missing();
					count_gec_wayfair_variation_product_title_webready();
					count_gec_houzz_variation_product_title_missing();
					count_gec_houzz_variation_product_title_webready();
					count_gec_home_depot_ca_variation_product_title_missing();
					count_gec_home_depot_ca_variation_product_title_webready();
					//CLIENT & LIVE RETAILER PRODUCT TITLES
					count_live_home_depot_product_title_missing();
					count_live_home_depot_product_title_webready();
					count_live_bbb_overstock_product_title_missing();
					count_live_bbb_overstock_product_title_webready();
					count_live_target_product_title_missing();
					count_live_target_product_title_webready();
					count_live_walmart_product_title_missing();
					count_live_walmart_product_title_webready();
					count_live_amazon_vc_product_title_missing();
					count_live_amazon_vc_product_title_webready();
					count_live_wayfair_product_title_missing();
					count_live_wayfair_product_title_webready();
					count_live_amazon_sc_product_title_missing();
					count_live_amazon_sc_product_title_webready();
					count_live_houzz_product_title_missing();
					count_live_houzz_product_title_webready();
					count_live_home_depot_ca_product_title_missing();
					count_live_home_depot_ca_product_title_webready();

					// Descriptions
					count_variation_description_missing();
					count_variation_description_webready();
					count_single_item_description_missing();
					count_single_item_description_webready();

					// Bullet Points (GEC 1-5 only - visible section)
					count_gec_bullet_point_1_missing();
					count_gec_bullet_point_1_webready();
					count_gec_bullet_point_2_missing();
					count_gec_bullet_point_2_webready();
					count_gec_bullet_point_3_missing();
					count_gec_bullet_point_3_webready();
					count_gec_bullet_point_4_missing();
					count_gec_bullet_point_4_webready();
					count_gec_bullet_point_5_missing();
					count_gec_bullet_point_5_webready();
					count_gec_bullet_point_6_missing();
					count_gec_bullet_point_6_webready();
					count_gec_bullet_point_7_missing();
					count_gec_bullet_point_7_webready();
					count_gec_bullet_point_8_missing();
					count_gec_bullet_point_8_webready();
					count_gec_bullet_point_9_missing();
					count_gec_bullet_point_9_webready();
					count_gec_bullet_point_10_missing();
					count_gec_bullet_point_10_webready();
					count_gec_bullet_point_11_missing();
					count_gec_bullet_point_11_webready();
					count_gec_bullet_point_12_missing();
					count_gec_bullet_point_12_webready();
					count_gec_bullet_point_13_missing();
					count_gec_bullet_point_13_webready();
					count_gec_bullet_point_14_missing();
					count_gec_bullet_point_14_webready();
					count_gec_bullet_point_15_missing();
					count_gec_bullet_point_15_webready();

				// Product Titles
				function count_gec_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_home_depot_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_home_depot_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bbb_overstock_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_bbb_overstock_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bbb_overstock_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bbb_overstock_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bbb_overstock_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_bbb_overstock_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bbb_overstock_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bbb_overstock_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_target_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_target_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_target_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_target_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_target_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_target_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_target_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_target_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_walmart_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_walmart_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_walmart_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_walmart_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_walmart_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_walmart_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_walmart_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_walmart_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_amazon_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_amazon_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_amazon_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_amazon_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_amazon_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_amazon_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_amazon_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_amazon_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_wayfair_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_wayfair_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_wayfair_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_wayfair_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_wayfair_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_wayfair_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_wayfair_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_wayfair_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_houzz_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_houzz_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_houzz_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_houzz_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_houzz_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_houzz_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_houzz_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_houzz_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_ca_single_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_ca_single_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_home_depot_ca_single_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_ca_single_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_ca_single_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_ca_single_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_home_depot_ca_single_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_ca_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				//GEC & GEC RETAILER VARIATION PRODUCT TITLES
				function count_gec_home_depot_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_home_depot_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_home_depot_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bbb_overstock_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_bbb_overstock_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bbb_overstock_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bbb_overstock_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bbb_overstock_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_bbb_overstock_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bbb_overstock_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bbb_overstock_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_target_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_target_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_target_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_target_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_target_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_target_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_target_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_target_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_walmart_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_walmart_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_walmart_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_walmart_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_walmart_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_walmart_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_walmart_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_walmart_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_amazon_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_amazon_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_amazon_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_amazon_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_amazon_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_amazon_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_amazon_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_amazon_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_wayfair_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_wayfair_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_wayfair_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_wayfair_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_wayfair_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_wayfair_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_wayfair_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_wayfair_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_houzz_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_houzz_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_houzz_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_houzz_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_houzz_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_houzz_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_houzz_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_houzz_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_ca_variation_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_ca_variation_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_home_depot_ca_variation_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_ca_variation_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_home_depot_ca_variation_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_gec_home_depot_ca_variation_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_home_depot_ca_variation_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_home_depot_ca_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }

				//CLIENT & LIVE RETAILER PRODUCT TITLES
				function count_live_home_depot_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_home_depot_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_home_depot_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_home_depot_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_home_depot_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_home_depot_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_home_depot_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_home_depot_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_bbb_overstock_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_bbb_overstock_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_bbb_overstock_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_bbb_overstock_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_bbb_overstock_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_bbb_overstock_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_bbb_overstock_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_bbb_overstock_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_target_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_target_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_target_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_target_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_target_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_target_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_target_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_target_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_walmart_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_walmart_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_walmart_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_walmart_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_walmart_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_walmart_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_walmart_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_walmart_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_amazon_vc_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_amazon_vc_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_amazon_vc_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_amazon_vc_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_amazon_vc_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_amazon_vc_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_amazon_vc_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_amazon_vc_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_wayfair_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_wayfair_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_wayfair_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_wayfair_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_wayfair_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_wayfair_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_wayfair_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_wayfair_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_amazon_sc_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_amazon_sc_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_amazon_sc_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_amazon_sc_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_amazon_sc_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_amazon_sc_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_amazon_sc_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_amazon_sc_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_houzz_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_houzz_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_houzz_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_houzz_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_houzz_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_houzz_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_houzz_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_houzz_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_home_depot_ca_product_title_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_home_depot_ca_product_title_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_live_home_depot_ca_product_title_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_home_depot_ca_product_title_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_live_home_depot_ca_product_title_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_product_title.php',
                type: 'POST',
                data: { count_live_home_depot_ca_product_title_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_live_home_depot_ca_product_title_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_live_home_depot_ca_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }

				// Descriptions
				function count_variation_description_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_descriptions.php',
                type: 'POST',
                data: { count_variation_description_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_variation_description_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_variation_description_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_variation_description_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_descriptions.php',
                type: 'POST',
                data: { count_variation_description_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_variation_description_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_variation_description_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_single_item_description_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_descriptions.php',
                type: 'POST',
                data: { count_single_item_description_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_single_item_description_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_single_item_description_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_single_item_description_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_descriptions.php',
                type: 'POST',
                data: { count_single_item_description_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_single_item_description_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_single_item_description_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				
				function count_gec_bullet_point_1_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_1_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_1_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_1_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_1_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_1_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_1_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_1_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_2_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_2_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_2_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_2_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_2_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_2_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_2_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_2_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_3_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_3_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_3_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_3_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_3_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_3_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_3_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_3_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_4_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_4_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_4_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_4_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_4_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_4_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_4_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_4_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_5_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_5_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_5_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_5_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_5_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_5_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_5_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_5_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_6_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_6_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_6_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_6_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_6_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_6_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_6_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_6_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_7_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_7_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_7_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_7_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_7_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_7_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_7_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_7_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_8_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_8_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_8_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_8_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_8_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_8_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_8_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_8_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_9_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_9_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_9_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_9_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_9_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_9_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_9_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_9_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_10_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_10_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_10_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_10_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_10_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_10_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_10_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_10_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_11_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_11_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_11_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_11_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_11_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_11_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_11_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_11_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_12_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_12_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_12_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_12_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_12_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_12_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_12_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_12_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_13_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_13_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_13_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_13_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_13_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_13_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_13_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_13_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_14_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_14_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_14_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_14_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_14_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_14_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_14_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_14_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				
				function count_gec_bullet_point_15_missing() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_15_missing: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString();
												$('#count_gec_bullet_point_15_missing').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_15_missing').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }
				function count_gec_bullet_point_15_webready() {
					var selectedClient = $('#initial_selectClient').val(); // get value from select
            $.ajax({
                url: 'product_content_ajax_bullet_points.php',
                type: 'POST',
                data: { count_gec_bullet_point_15_webready: "1",
									client_prefix: selectedClient,
                    select_stock: $('#select_stock').val() },
                dataType: 'json',
                success: function (response) {
									var countNumber = parseInt(response.count);
										if (!isNaN(countNumber)) {
												var formattedCount = countNumber.toLocaleString() + '%';
												$('#count_gec_bullet_point_15_webready').html(formattedCount);
										} else {
												console.error("Invalid count value:", response.count);
										}
                },
                error: function () {
                    $('#count_gec_bullet_point_15_webready').html('<span class="spinner-border spinner-border-sm"></span>');
                }
            });
        }

				//Product Titles
				$('#count_gec_single_product_title_missing, #count_gec_single_product_title_webready, #count_gec_variation_product_title_missing, #count_gec_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//GEC &  GEC RETAILER SINGLE PRODUCT TITLES
				$('#count_gec_home_depot_single_product_title_missing, #count_gec_home_depot_single_product_title_webready, #count_gec_bbb_overstock_single_product_title_missing, #count_gec_bbb_overstock_single_product_title_webready, #count_gec_target_single_product_title_missing, #count_gec_target_single_product_title_webready, #count_gec_walmart_single_product_title_missing, #count_gec_walmart_single_product_title_webready, #count_gec_amazon_single_product_title_missing, #count_gec_amazon_single_product_title_webready, #count_gec_wayfair_single_product_title_missing, #count_gec_wayfair_single_product_title_webready, #count_gec_houzz_single_product_title_missing, #count_gec_houzz_single_product_title_webready, #count_gec_home_depot_ca_single_product_title_missing, #count_gec_home_depot_ca_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//GEC &  GEC RETAILER VARIATION PRODUCT TITLES
				$('#count_gec_home_depot_variation_product_title_missing, #count_gec_home_depot_variation_product_title_webready, #count_gec_bbb_overstock_variation_product_title_missing, #count_gec_bbb_overstock_variation_product_title_webready, #count_gec_target_variation_product_title_missing, #count_gec_target_variation_product_title_webready, #count_gec_walmart_variation_product_title_missing, #count_gec_walmart_variation_product_title_webready, #count_gec_amazon_variation_product_title_missing, #count_gec_amazon_variation_product_title_webready, #count_gec_wayfair_variation_product_title_missing, #count_gec_wayfair_variation_product_title_webready, #count_gec_houzz_variation_product_title_missing, #count_gec_houzz_variation_product_title_webready, #count_gec_home_depot_ca_variation_product_title_missing, #count_gec_home_depot_ca_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//CLIENT & LIVE RETAILER PRODUCT TITLES
				$('#count_live_home_depot_product_title_missing, #count_live_home_depot_product_title_webready, #count_live_bbb_overstock_product_title_missing, #count_live_bbb_overstock_product_title_webready, #count_live_target_product_title_missing, #count_live_target_product_title_webready, #count_live_walmart_product_title_missing, #count_live_walmart_product_title_webready, #count_live_amazon_vc_product_title_missing, #count_live_amazon_vc_product_title_webready, #count_live_wayfair_product_title_missing, #count_live_wayfair_product_title_webready, #count_live_amazon_sc_product_title_missing, #count_live_amazon_sc_product_title_webready, #count_live_houzz_product_title_missing, #count_live_houzz_product_title_webready, #count_live_home_depot_ca_product_title_missing, #count_live_home_depot_ca_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');

				//Descriptions
				$('#count_variation_description_missing, #count_variation_description_webready, #count_single_item_description_missing, #count_single_item_description_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//Bullet Points - Only show spinners for GEC 1-5 initially
				$('#count_gec_bullet_point_1_missing, #count_gec_bullet_point_1_webready, #count_gec_bullet_point_2_missing, #count_gec_bullet_point_2_webready, #count_gec_bullet_point_3_missing, #count_gec_bullet_point_3_webready, #count_gec_bullet_point_4_missing, #count_gec_bullet_point_4_webready, #count_gec_bullet_point_5_missing, #count_gec_bullet_point_5_webready').html('<span class="spinner-border spinner-border-sm"></span>');

				// Bind change event
				//select in stock
				$('#initial_selectClient, #select_stock').on('change click', function () {
				//Product Titles
				$('#count_gec_single_product_title_missing, #count_gec_single_product_title_webready, #count_gec_variation_product_title_missing, #count_gec_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//GEC &  GEC RETAILER SINGLE PRODUCT TITLES
				$('#count_gec_home_depot_single_product_title_missing, #count_gec_home_depot_single_product_title_webready, #count_gec_bbb_overstock_single_product_title_missing, #count_gec_bbb_overstock_single_product_title_webready, #count_gec_target_single_product_title_missing, #count_gec_target_single_product_title_webready, #count_gec_walmart_single_product_title_missing, #count_gec_walmart_single_product_title_webready, #count_gec_amazon_single_product_title_missing, #count_gec_amazon_single_product_title_webready, #count_gec_wayfair_single_product_title_missing, #count_gec_wayfair_single_product_title_webready, #count_gec_houzz_single_product_title_missing, #count_gec_houzz_single_product_title_webready, #count_gec_home_depot_ca_single_product_title_missing, #count_gec_home_depot_ca_single_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//GEC &  GEC RETAILER VARIATION PRODUCT TITLES
				$('#count_gec_home_depot_variation_product_title_missing, #count_gec_home_depot_variation_product_title_webready, #count_gec_bbb_overstock_variation_product_title_missing, #count_gec_bbb_overstock_variation_product_title_webready, #count_gec_target_variation_product_title_missing, #count_gec_target_variation_product_title_webready, #count_gec_walmart_variation_product_title_missing, #count_gec_walmart_variation_product_title_webready, #count_gec_amazon_variation_product_title_missing, #count_gec_amazon_variation_product_title_webready, #count_gec_wayfair_variation_product_title_missing, #count_gec_wayfair_variation_product_title_webready, #count_gec_houzz_variation_product_title_missing, #count_gec_houzz_variation_product_title_webready, #count_gec_home_depot_ca_variation_product_title_missing, #count_gec_home_depot_ca_variation_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//CLIENT & LIVE RETAILER PRODUCT TITLES
				$('#count_live_home_depot_product_title_missing, #count_live_home_depot_product_title_webready, #count_live_bbb_overstock_product_title_missing, #count_live_bbb_overstock_product_title_webready, #count_live_target_product_title_missing, #count_live_target_product_title_webready, #count_live_walmart_product_title_missing, #count_live_walmart_product_title_webready, #count_live_amazon_vc_product_title_missing, #count_live_amazon_vc_product_title_webready, #count_live_wayfair_product_title_missing, #count_live_wayfair_product_title_webready, #count_live_amazon_sc_product_title_missing, #count_live_amazon_sc_product_title_webready, #count_live_houzz_product_title_missing, #count_live_houzz_product_title_webready, #count_live_home_depot_ca_product_title_missing, #count_live_home_depot_ca_product_title_webready').html('<span class="spinner-border spinner-border-sm"></span>');

				//Descriptions
				$('#count_variation_description_missing, #count_variation_description_webready, #count_single_item_description_missing, #count_single_item_description_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				//Bullet Points - Show spinners for GEC 1-5 initially
				$('#count_gec_bullet_point_1_missing, #count_gec_bullet_point_1_webready, #count_gec_bullet_point_2_missing, #count_gec_bullet_point_2_webready, #count_gec_bullet_point_3_missing, #count_gec_bullet_point_3_webready, #count_gec_bullet_point_4_missing, #count_gec_bullet_point_4_webready, #count_gec_bullet_point_5_missing, #count_gec_bullet_point_5_webready').html('<span class="spinner-border spinner-border-sm"></span>');

				// Show spinners for GEC 6-15 if section is expanded
				if ($('#additionalBulletPoints').is(':visible')) {
					$('#count_gec_bullet_point_6_missing, #count_gec_bullet_point_6_webready, #count_gec_bullet_point_7_missing, #count_gec_bullet_point_7_webready, #count_gec_bullet_point_8_missing, #count_gec_bullet_point_8_webready, #count_gec_bullet_point_9_missing, #count_gec_bullet_point_9_webready, #count_gec_bullet_point_10_missing, #count_gec_bullet_point_10_webready, #count_gec_bullet_point_11_missing, #count_gec_bullet_point_11_webready, #count_gec_bullet_point_12_missing, #count_gec_bullet_point_12_webready, #count_gec_bullet_point_13_missing, #count_gec_bullet_point_13_webready, #count_gec_bullet_point_14_missing, #count_gec_bullet_point_14_webready, #count_gec_bullet_point_15_missing, #count_gec_bullet_point_15_webready').html('<span class="spinner-border spinner-border-sm"></span>');
				}

					//product titles
					count_gec_single_product_title_missing();
					count_gec_single_product_title_webready();
					count_gec_variation_product_title_missing();
					count_gec_variation_product_title_webready();
					//GEC &  GEC RETAILER SINGLE PRODUCT TITLES
					count_gec_home_depot_single_product_title_missing();
					count_gec_home_depot_single_product_title_webready();
					count_gec_bbb_overstock_single_product_title_missing();
					count_gec_bbb_overstock_single_product_title_webready();
					count_gec_target_single_product_title_missing();
					count_gec_target_single_product_title_webready();
					count_gec_walmart_single_product_title_missing();
					count_gec_walmart_single_product_title_webready();
					count_gec_amazon_single_product_title_missing();
					count_gec_amazon_single_product_title_webready();
					count_gec_wayfair_single_product_title_missing();
					count_gec_wayfair_single_product_title_webready();
					count_gec_houzz_single_product_title_missing();
					count_gec_houzz_single_product_title_webready();
					count_gec_home_depot_ca_single_product_title_missing();
					count_gec_home_depot_ca_single_product_title_webready();
					//GEC & GEC RETAILER VARIATION PRODUCT TITLES
					count_gec_home_depot_variation_product_title_missing();
					count_gec_home_depot_variation_product_title_webready();
					count_gec_bbb_overstock_variation_product_title_missing();
					count_gec_bbb_overstock_variation_product_title_webready();
					count_gec_target_variation_product_title_missing();
					count_gec_target_variation_product_title_webready();
					count_gec_walmart_variation_product_title_missing();
					count_gec_walmart_variation_product_title_webready();
					count_gec_amazon_variation_product_title_missing();
					count_gec_amazon_variation_product_title_webready();
					count_gec_wayfair_variation_product_title_missing();
					count_gec_wayfair_variation_product_title_webready();
					count_gec_houzz_variation_product_title_missing();
					count_gec_houzz_variation_product_title_webready();
					count_gec_home_depot_ca_variation_product_title_missing();
					count_gec_home_depot_ca_variation_product_title_webready();
					//CLIENT & LIVE RETAILER PRODUCT TITLES
					count_live_home_depot_product_title_missing();
					count_live_home_depot_product_title_webready();
					count_live_bbb_overstock_product_title_missing();
					count_live_bbb_overstock_product_title_webready();
					count_live_target_product_title_missing();
					count_live_target_product_title_webready();
					count_live_walmart_product_title_missing();
					count_live_walmart_product_title_webready();
					count_live_amazon_vc_product_title_missing();
					count_live_amazon_vc_product_title_webready();
					count_live_wayfair_product_title_missing();
					count_live_wayfair_product_title_webready();
					count_live_amazon_sc_product_title_missing();
					count_live_amazon_sc_product_title_webready();
					count_live_houzz_product_title_missing();
					count_live_houzz_product_title_webready();
					count_live_home_depot_ca_product_title_missing();
					count_live_home_depot_ca_product_title_webready();

					// Descriptions
					count_variation_description_missing();
					count_variation_description_webready();
					count_single_item_description_missing();
					count_single_item_description_webready();

					// Bullet Points (GEC 1-5 only - visible section)
					count_gec_bullet_point_1_missing();
					count_gec_bullet_point_1_webready();
					count_gec_bullet_point_2_missing();
					count_gec_bullet_point_2_webready();
					count_gec_bullet_point_3_missing();
					count_gec_bullet_point_3_webready();
					count_gec_bullet_point_4_missing();
					count_gec_bullet_point_4_webready();
					count_gec_bullet_point_5_missing();
					count_gec_bullet_point_5_webready();

					// Load GEC 6-15 and Client 1-15 if section is expanded
					if ($('#additionalBulletPoints').is(':visible')) {
						count_gec_bullet_point_6_missing();
						count_gec_bullet_point_6_webready();
						count_gec_bullet_point_7_missing();
						count_gec_bullet_point_7_webready();
						count_gec_bullet_point_8_missing();
						count_gec_bullet_point_8_webready();
						count_gec_bullet_point_9_missing();
						count_gec_bullet_point_9_webready();
						count_gec_bullet_point_10_missing();
						count_gec_bullet_point_10_webready();
						count_gec_bullet_point_11_missing();
						count_gec_bullet_point_11_webready();
						count_gec_bullet_point_12_missing();
						count_gec_bullet_point_12_webready();
						count_gec_bullet_point_13_missing();
						count_gec_bullet_point_13_webready();
						count_gec_bullet_point_14_missing();
						count_gec_bullet_point_14_webready();
						count_gec_bullet_point_15_missing();
						count_gec_bullet_point_15_webready();
						
					}

					// Load additional bullet points only if section is expanded
					if ($('#additionalBulletPoints').is(':visible')) {
						loadAdditionalBulletPoints();
					}
	});

	// Function to load additional bullet points (GEC 6-15)
	var additionalBulletPointsLoaded = false;
	function loadAdditionalBulletPoints() {
		if (!additionalBulletPointsLoaded) {
			// Show spinners for GEC bullet points 6-15 and Client bullet points 1-15
			$('#count_gec_bullet_point_6_missing, #count_gec_bullet_point_6_webready, #count_gec_bullet_point_7_missing, #count_gec_bullet_point_7_webready, #count_gec_bullet_point_8_missing, #count_gec_bullet_point_8_webready, #count_gec_bullet_point_9_missing, #count_gec_bullet_point_9_webready, #count_gec_bullet_point_10_missing, #count_gec_bullet_point_10_webready, #count_gec_bullet_point_11_missing, #count_gec_bullet_point_11_webready, #count_gec_bullet_point_12_missing, #count_gec_bullet_point_12_webready, #count_gec_bullet_point_13_missing, #count_gec_bullet_point_13_webready, #count_gec_bullet_point_14_missing, #count_gec_bullet_point_14_webready, #count_gec_bullet_point_15_missing, #count_gec_bullet_point_15_webready').html('<span class="spinner-border spinner-border-sm"></span>');

			// Load GEC bullet points 6-15
			count_gec_bullet_point_6_missing();
			count_gec_bullet_point_6_webready();
			count_gec_bullet_point_7_missing();
			count_gec_bullet_point_7_webready();
			count_gec_bullet_point_8_missing();
			count_gec_bullet_point_8_webready();
			count_gec_bullet_point_9_missing();
			count_gec_bullet_point_9_webready();
			count_gec_bullet_point_10_missing();
			count_gec_bullet_point_10_webready();
			count_gec_bullet_point_11_missing();
			count_gec_bullet_point_11_webready();
			count_gec_bullet_point_12_missing();
			count_gec_bullet_point_12_webready();
			count_gec_bullet_point_13_missing();
			count_gec_bullet_point_13_webready();
			count_gec_bullet_point_14_missing();
			count_gec_bullet_point_14_webready();
			count_gec_bullet_point_15_missing();
			count_gec_bullet_point_15_webready();


			additionalBulletPointsLoaded = true;
		}
	}

	// Toggle button handler for bullet points
	$('#toggleBulletPointsBtn').on('click', function() {
		var $btn = $(this);
		var $additionalSection = $('#additionalBulletPoints');

		if ($additionalSection.is(':visible')) {
			// Hide the section
			$additionalSection.slideUp(300);
			$btn.html('<i class="fas fa-chevron-down mr-1"></i> More');
		} else {
			// Show the section and load data if not already loaded
			$additionalSection.slideDown(300);
			$btn.html('<i class="fas fa-chevron-up mr-1"></i> Less');
			loadAdditionalBulletPoints();
		}
	});

	// ========== Dynamic Retailer Permission Functions ==========

	/**
	 * Initialize client retailer permissions on page load
	 */
	function initializeClientRetailerPermissions() {
		loadRetailerPermissions();
	}

	/**
	 * Load retailer permissions from the server
	 */
	function loadRetailerPermissions() {
		var selectedClient = $('#initial_selectClient').val();

		console.log('Loading retailer permissions for client:', selectedClient);

		$.ajax({
			url: 'get_client_retailer_permissions.php',
			type: 'POST',
			data: { client_prefix: selectedClient },
			dataType: 'json',
			success: function(response) {
				console.log('Retailer permissions response:', response);
				if (response.success) {
					console.log('Updating retailer visibility with permissions:', response.permissions);
					updateRetailerVisibility(response.permissions);
				} else {
					console.error('Failed to load retailer permissions:', response.message);
				}
			},
			error: function(xhr, status, error) {
				console.error('Error loading retailer permissions:', error);
				console.error('XHR:', xhr);
			}
		});
	}

	/**
	 * Update retailer section visibility based on permissions
	 * @param {Object} permissions - Object with retailer codes as keys and boolean values
	 */
	function updateRetailerVisibility(permissions) {
		console.log('Updating retailer visibility...');
		var visibleCount = 0;

		// Show/hide retailer sections based on permissions
		$('.retailer-section').each(function() {
			const retailer = $(this).data('retailer');
			if (permissions[retailer]) {
				console.log('Showing retailer section:', retailer);
				$(this).show();
				visibleCount++;
			} else {
				console.log('Hiding retailer section:', retailer);
				$(this).hide();
			}
		});

		console.log('Total visible retailer sections:', visibleCount);
	}

	// Reload retailer permissions when client selection changes
	$('#initial_selectClient').on('change', function() {
		loadRetailerPermissions();
	});

});
</script>