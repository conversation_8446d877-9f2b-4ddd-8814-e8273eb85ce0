<?php

$tab_default = '';
$tab_product_titles_3 = '';

if (isset($_GET['select1'])) {
    $select1 = $_GET['select1'];
    $select1 = htmlspecialchars(trim($select1));

    if ($select1 == 'clientprovidedproducttitle') {
        $tab_default = 'selected readonly';
    }

} else {
    $select1 = '';
}

if (isset($_GET['select2'])) {
    $select2 = $_GET['select2'];
    $select2 = htmlspecialchars(trim($select2));

    if ($select2 == 'gec_retailer_single_product_title') {
        $tab_product_titles_3 = 'selected';
        $tab_default = ''; // Clear default selection when select2 is specified
    }

} else {
    $select2 = '';
}

if(empty($tab_default) && empty($tab_product_titles_3)){
    $tab_default = 'selected';
}
?>