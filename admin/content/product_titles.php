<!DOCTYPE html>
<html lang="en" class="root-text-sm">
    <head>
        <title>Product Titles | Grow ECommerce Inc.</title>
        <?php include ('../includes/header.php'); ?>
				<link rel="stylesheet" media="screen, print" href="<?=base_url?>assets/css/notifications/toastr/toastr.css">
    </head>
    <!-- BEGIN Body -->
    <body class="<?=$htmlclass?>">
        <!-- DOC: script to save and load page settings -->
        <?php include ('../includes/page_settings_save.php'); ?>
        <!-- BEGIN Page Wrapper -->
        <div class="page-wrapper">
            <div class="page-inner">
                <!-- BEGIN Left Aside -->
                <?php include ('../includes/sidebar.php'); ?>
                <!-- END Left Aside -->
                <div class="page-content-wrapper">
                    <!-- BEGIN Page Header -->
                    <?php include ('../includes/navbar.php'); ?>
                    <!-- END Page Header -->
                    <!-- BEGIN Page Content -->
                    <!-- the #js-page-content id is needed for some plugins to initialize -->
                    <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);">Content</a></li>
                            <li class="breadcrumb-item">Product Titles</li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
                       
<div class="row">
		<div class="col-xl-12">
			<div id="panel-1" class="panel">
                <div class="panel-hdr">
                                        <h2>Product Titles <span class="fw-300"><i>Table</i></span></h2>.
                                        <div class="panel-toolbar">
                                            <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                                            <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                                            <button class="btn btn-panel" data-action="panel-close" data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                                        </div>
                                    </div>
						
				<div class="panel-container show">
					<div class="panel-content">
						
						
						  
	<div class="tab-content p-3">
		<div class="row overflow-hidden mb-2">
			<div class="panel-toolbar ml-2 mr-2">
			<button type="button" class="btn btn-primary bg-trans-gradient m-1" id="step1Container" data-toggle="modal" data-target="#modal_import_product_titles"><i class="fal fa-upload"></i> Import</button>
			<button type="button" class="btn btn-outline-info m-1" id="resetColReorderBtn"><i class="fal fa-columns"></i> Reset Column</button>
			
					
			
			</div>
				<div class="panel-toolbar ml-auto mr-2">
					<button type="button" class="btn btn-primary bg-trans-gradient m-1" id="auditHistory_btn" data-toggle="modal" data-target="#auditModal" onclick="view_audit_logsModal(this)">
						<i class="fal fa-history"></i> Audit History
					</button>
					<button type="button" class="btn btn-primary bg-trans-gradient m-1" id="logHistory_btn" data-toggle="modal" data-target="#logsModal">
						<i class="fal fa-history"></i> Log History
					</button>
				</div>
			
			</div>

			<?php include ('filter_table_view.php'); ?>
			<div class="row overflow-hidden">
				 <div class="col-sm-12">
					<div class="row">
                        							<div class="col-lg-3 m-1">

                            <div class="scrollmenu">
                            <select class="form-control" id="view-data-options" onchange="changeTab(this.value)" <?= !empty($_GET['select1']) ? 'disabled' : '' ?> >
                                    <option value="#tab-alldata">Select Table Views</option>
                                    <option value="#tab-default" <?= $tab_default; ?> >GEC & Client Product Titles</option>
                                    <option value="#tab-product-titles-3" <?= $tab_product_titles_3; ?>>GEC & GEC Retailer Single Product Titles</option>
                                    <option value="#tab-product-titles-4">GEC & GEC Retailer Variation Product Titles</option>
                                    <option value="#tab-product-titles-5">Client & Live Retailer Product Titles</option>
                                    <option value="#tab-product-titles-6">Home Depot Product Titles</option>
                                    <option value="#tab-product-titles-7">BBB/Overstock Product Titles</option>
                                    <option value="#tab-product-titles-8">Target Product Titles</option>
                                    <option value="#tab-product-titles-9">Walmart Product Titles</option>
                                    <option value="#tab-product-titles-10">Amazon VC Product Titles</option>
                                    <option value="#tab-product-titles-11">Wayfair Product Titles</option>
                                    <option value="#tab-product-titles-12">Amazon SC Product Titles</option>
                                    <option value="#tab-product-titles-13">Houzz Product Titles</option>
                                    <option value="#tab-product-titles-14">Home Depot CA Product Titles</option>

                            </select>

							<ul class="nav nav-tabs nav-tabs-clean d-none" role="tablist">
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-alldata" role="tab">All Data</a></li>
									<li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#tab-default" role="tab">GEC & Client Product Titles</a></li>
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-3" role="tab">GEC & GEC Retailer Single Product Titles</a></li>
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-4" role="tab">GEC & GEC Retailer Variation Product Titles</a></li>
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-5" role="tab">Client & Live Retailer Product Titles</a></li>
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-6" role="tab">Home Depot Product Titles</a></li>
									<li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-7" role="tab">BBB/Overstock Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-8" role="tab">Target Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-9" role="tab">Walmart Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-10" role="tab">Amazon VC Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-11" role="tab">Wayfair Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-12" role="tab">Amazon SC Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-13" role="tab">Houzz Product Titles</a></li>
                                    <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab-product-titles-14" role="tab">Home Depot CA Product Titles</a></li>
							</ul>
								</div>
                                                    </div>
							<div class="col-lg-2 m-1">
								<select class="select2 form-control w-100" id="initial_select1" aria-hidden="true" <?= !empty($_GET['select1']) ? 'disabled' : '' ?> >
									<option value="ActivePDiscFuture" selected>Active, PDisc & Future</option>
									<option value="Active">Active</option>
									<option value="PDisc">PDisc</option>
									<option value="Future">Future</option>
								</select>
							</div>

							<div class="col-lg-2 m-1">
								<select class="select2 form-control w-100" id="initial_select2" aria-hidden="true" <?= !empty($_GET['select1']) ? 'disabled' : '' ?> >
									<option value="">All GEC Variation Type</option>
									<option value="unknown">Unknown</option>
									<option value="none">None</option>
									<option value="size">Size</option>
									<option value="color">Color</option>
									<option value="size & color">Size & Color (data set)</option>
								</select>
							</div>

							<div class="col-lg-2 m-1">
								<select class="select2 form-control w-100" id="initial_select3" aria-hidden="true" <?= !empty($_GET['select1']) ? 'disabled' : '' ?> >
									<option value="">All GEC Retailer Managed</option>
									<option value="Home Depot USA">Home Depot USA</option>
									<option value="BBB/Overstock">BBB/Overstock</option>
									<option value="Target">Target</option>
									<option value="Walmart">Walmart</option>
									<option value="Amazon VC">Amazon VC</option>
									<option value="Wayfair">Wayfair</option>
									<option value="Amazon SC">Amazon SC</option>
									<option value="Houzz">Houzz</option>
									<option value="Home Depot CA">Home Depot CA</option>
								</select>
							</div>

                            <!-- <div class="ml-1 col-md-2">
                                <select class="select2 form-control w-100" id="initial_select5" aria-hidden="true">
                                    <option value=" ">Select Retailer Requirements</option>
                                    <option value="Home Depot">Home Depot</option>
                                    <option value="BBB/Overstock">BBB/Overstock</option>
                                    <option value="Target">Target</option>
                                    <option value="Walmart">Walmart</option>
                                    <option value="Wayfair">Wayfair</option>
                                    <option value="Amazon">Amazon</option>
                                </select>
                            </div> -->
					</div>

				 </div>
			</div>
			
			<!-- tab -->
			<div class="tab-content">
    <!-- Tab 1: Full data table -->
    <div class="tab-pane fade show" id="tab-alldata" role="tabpanel" aria-labelledby="tab-alldata">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-1" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>GEC Variation Type</th>
										<th>GEC Single Product Title</th>
										<th>GEC Variation Product Title</th>
										<th>Client Product Title</th>
										<th>GEC Home Depot Single Product Title</th>
										<th>GEC Home Depot Variation Product Title</th>
										<th>Live Home Depot Product Title</th>
										<th>Home Depot Product Title Status</th>
										<th>GEC BBB/Overstock Single Product Title</th>
										<th>GEC BBB/Overstock Variation Product Title</th>
										<th>Live BBB/Overstock Product Title</th>
										<th>BBB/Overstock Product Title Status</th>
										<th>GEC Target Single Product Title</th>
										<th>GEC Target Variation Product Title</th>
										<th>Live Target Product Title</th>
										<th>Target Product Title Status</th>
										<th>GEC Walmart Single Product Title</th>
										<th>GEC Walmart Variation Product Title</th>
										<th>Live Walmart Product Title</th>
										<th>Walmart Product Title Status</th>
										<th>GEC Amazon Single Product Title</th>
										<th>GEC Amazon Variation Product Title</th>
										<th>Live Amazon VC Product Title</th>
										<th>Amazon VC Product Title Status</th>
										<th>GEC Wayfair Single Product Title</th>
										<th>GEC Wayfair Variation Product Title</th>
										<th>Live Wayfair Product Title</th>
										<th>Wayfair Product Title Status</th>
										<th>Live Amazon SC Product Title</th>
										<th>Amazon SC Product Title Status</th>
										<th>GEC Houzz Single Product Title</th>
										<th>GEC Houzz Variation Product Title</th>
										<th>Live Houzz Product Title</th>
										<th>Houzz Product Title Status</th>
										<th>GEC Home Depot CA Single Product Title</th>
										<th>GEC Home Depot CA Variation Product Title</th>
										<th>Live Home Depot CA Product Title</th>
										<th>Home Depot CA Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

		<!-- default view, gec & client product titles -->
		<div class="tab-pane fade active show" id="tab-default" role="tabpanel" aria-labelledby="tab-default">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-2" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>GEC Variation Type</th>
										<th>GEC Single Product Title</th>
										<th>GEC Variation Product Title</th>
										<th>Client Provided Product Title</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

		<!-- tab 3 view, GEC & GEC Retailer Single Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-3" role="tabpanel" aria-labelledby="tab-product-titles-3">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-3" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>GEC Product Title</th>
										<th>GEC Home Depot Single Product Title</th>
										<th>GEC BBB/Overstock Single Product Title</th>
										<th>GEC Target Single Product Title</th>
										<th>GEC Walmart Single Product Title</th>
										<th>GEC Amazon Single Product Title</th>
										<th>GEC Wayfair Single Product Title</th>
										<!-- <th>GEC Amazon SC Single Product Title</th> -->
										<th>GEC Houzz Single Product Title</th>
										<th>GEC Home Depot CA Single Product Title</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

		<!-- tab 4 view, GEC & GEC Retailer Variation Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-4" role="tabpanel" aria-labelledby="tab-product-titles-4">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-4" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>GEC Product Title</th>
										<th>GEC Home Depot Variation Product Title</th>
										<th>GEC BBB/Overstock Variation Product Title</th>
										<th>GEC Target Variation Product Title</th>
										<th>GEC Walmart Variation Product Title</th>
										<th>GEC Amazon Variation Product Title</th>
										<th>GEC Wayfair Variation Product Title</th>
										<!-- <th>GEC Amazon SC Variation Product Title</th> -->
										<th>GEC Houzz Variation Product Title</th>
										<th>GEC Home Depot CA Variation Product Title</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

		<!-- tab 5 view, Client & Live Retailer Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-5" role="tabpanel" aria-labelledby="tab-product-titles-5">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-5" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>GEC Product Title</th>
										<th>Live Home Depot Product Title</th>
										<th>Live BBB/Overstock Product Title</th>
										<th>Live Target Product Title</th>
										<th>Live Walmart Product Title</th>
										<th>Live Amazon VC Product Title</th>
										<th>Live Wayfair Product Title</th>
										<th>Live Amazon SC Product Title</th>
										<th>Live Houzz Product Title</th>
										<th>Live Home Depot CA Product Title</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

		<!-- tab 6 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-6" role="tabpanel" aria-labelledby="tab-product-titles-6">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-6" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>OMSID</th>
										<th>THD Store SKU</th>
										<th>GEC Home Depot Single Product Title</th>
										<th>GEC Home Depot Variation Product Title</th>
										<th>Live Home Depot Product Title</th>
										<th>Home Depot Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <!-- tab 7 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-7" role="tabpanel" aria-labelledby="tab-product-titles-7">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-7" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>BBB/Overstock SKU</th>
										<th>BBB/Overstock Product ID</th>
										<th>GEC BBB/Overstock Single Product Title</th>
										<th>GEC BBB/Overstock Variation Product Title</th>
										<th>Live BBB/Overstock Product Title</th>
										<th>BBB/Overstock Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <!-- tab 8 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-8" role="tabpanel" aria-labelledby="tab-product-titles-8">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-8" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>TCIN</th>
										<th>Parent TCIN</th>
                                        <th>DPCI</th>
										<th>GEC Target Single Product Title</th>
										<th>GEC Target Variation Product Title</th>
										<th>Live Target Product Title</th>
										<th>Target Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <!-- tab 9 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-9" role="tabpanel" aria-labelledby="tab-product-titles-9">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-9" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>Walmart Item #</th>
										<th>GEC Walmart Single Product Title</th>
										<th>GEC Walmart Variation Product Title</th>
										<th>Live Walmart Product Title</th>
										<th>Walmart Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

     <!-- tab 10 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-10" role="tabpanel" aria-labelledby="tab-product-titles-10">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-10" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>ASIN</th>
										<th>GEC Amazon Single Product Title</th>
										<th>GEC Amazon Variation Product Title</th>
										<th>Live Amazon VC Product Title</th>
										<th>Amazon VC Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

         <!-- tab 11 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-11" role="tabpanel" aria-labelledby="tab-product-titles-11">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-11" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>Wayfair SKU</th>
										<th>GEC Wayfair Single Product Title</th>
										<th>GEC Wayfair Variation Product Title</th>
										<th>Live Wayfair Product Title</th>
										<th>Wayfair Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

             <!-- tab 12 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-12" role="tabpanel" aria-labelledby="tab-product-titles-12">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-12" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>ASIN</th>
										<th>GEC Amazon Single Product Title</th>
										<th>GEC Amazon Variation Product Title</th>
										<th>Live Amazon SC Product Title</th>
										<th>Amazon SC Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

                 <!-- tab 13 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-13" role="tabpanel" aria-labelledby="tab-product-titles-13">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-13" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>Houzz Product ID</th>
										<th>GEC Houzz Single Product Title</th>
										<th>GEC Houzz Variation Product Title</th>
										<th>Live Houzz Product Title</th>
										<th>Houzz Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    		<!-- tab 14 view, Home Depot Product Titles -->
		<div class="tab-pane fade show" id="tab-product-titles-14" role="tabpanel" aria-labelledby="tab-product-titles-14">
        <div class="d-flex mb-3"></div>
        <table id="dt-product-titles-14" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
            <thead class="thead-dark">
                <tr>
                    <th>GECID</th>
                    <th>GEC Item</th>
										<th>OMSID</th>
										<th>THD Store SKU</th>
										<th>GEC Home Depot CA Single Product Title</th>
										<th>GEC Home Depot CA Variation Product Title</th>
										<th>Live Home Depot CA Product Title</th>
										<th>Home Depot CA Product Title Status</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>
<div class="tab-pane fade" id="tab_default" role="tabpanel">
<div class="tab-content py-3">	

<script>
$(document).ready(function () {
    var dataTable; // Declare once globally
		var dataTable2;
		var dataTable3;
		var dataTable4;
		var dataTable5;
        var dataTable6;
        var dataTable7;
        var dataTable8;
        var dataTable9;
        var dataTable10;
        var dataTable11;
        var dataTable12;
        var dataTable13;
        var dataTable14;

        function newexportaction(e, dt, button, config) {
            var self = this;
            var oldStart = dt.settings()[0]._iDisplayStart;
            var oldLength = dt.settings()[0]._iDisplayLength;

            // Get the current date and time for the filename
            var currentDate = new Date();
            var formattedDate = currentDate.toLocaleDateString('en-US').replace(/\//g, '-'); // Format: DD-MM-YYYY
            var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '.'); // Format: HH.MM.SS
            var timestamp = formattedDate + ' ' + formattedTime;

            // Custom filename
            var customFilename = document.title + ' ' + timestamp;

            // Check if any rows are selected
            var selectedRows = dt.rows({ selected: true }).count();
            var allDataRequested = selectedRows === 0;

            // Set export options
            config.filename = customFilename;
            config.exportOptions = {
                columns: ':visible', // Export only visible columns
                orthogonal: 'export',
                format: {
                    body: function(data, row, column, node) {
                        // Return an empty string if the data is null
                        return data === null ? '' : data;
                    }
                }
            };

            if (allDataRequested) {
                // If no rows are selected, export all data
                dt.one('preXhr', function (e, s, data) {
                    // Load all data from the server
                    data.start = 0;
                    data.length = 2147483647;

                    dt.one('preDraw', function (e, settings) {
                        // Set the filename with the timestamp
                        config.filename = customFilename;

                        // Customize export options to handle null values
                        config.exportOptions = {
                            orthogonal: 'export',
                            format: {
                                body: function(data, row, column, node) {
                                    // Return an empty string if the data is null
                                    return data === null ? '' : data;
                                }
                            }
                        };

                        // Call the original action function based on the button type
                        if (button[0].className.indexOf('buttons-copy') >= 0) {
                            $.fn.dataTable.ext.buttons.copyHtml5.action.call(self, e, dt, button, config);
                        } else if (button[0].className.indexOf('buttons-excel') >= 0) {
                            $.fn.dataTable.ext.buttons.excelHtml5.available(dt, config) ?
                                $.fn.dataTable.ext.buttons.excelHtml5.action.call(self, e, dt, button, config) :
                                $.fn.dataTable.ext.buttons.excelFlash.action.call(self, e, dt, button, config);
                        } else if (button[0].className.indexOf('buttons-csv') >= 0) {
                            $.fn.dataTable.ext.buttons.csvHtml5.available(dt, config) ?
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(self, e, dt, button, config) :
                                $.fn.dataTable.ext.buttons.csvFlash.action.call(self, e, dt, button, config);
                        } else if (button[0].className.indexOf('buttons-pdf') >= 0) {
                            $.fn.dataTable.ext.buttons.pdfHtml5.available(dt, config) ?
                                $.fn.dataTable.ext.buttons.pdfHtml5.action.call(self, e, dt, button, config) :
                                $.fn.dataTable.ext.buttons.pdfFlash.action.call(self, e, dt, button, config);
                        } else if (button[0].className.indexOf('buttons-print') >= 0) {
                            $.fn.dataTable.ext.buttons.print.action(e, dt, button, config);
                        }

                        dt.one('preXhr', function (e, s, data) {
                            // Restore the original paging
                            settings._iDisplayStart = oldStart;
                            data.start = oldStart;
                        });

                        // Reload the grid with the original page
                        setTimeout(dt.ajax.reload, 0);

                        // Prevent rendering of the full data to the DOM
                        return false;
                    });
                });
            } else {
                // Export only selected rows
                config.exportOptions.modifier = {
                    selected: true // Export only selected rows
                };

                // Call the original action function based on the button type
                if (button[0].className.indexOf('buttons-copy') >= 0) {
                    $.fn.dataTable.ext.buttons.copyHtml5.action.call(self, e, dt, button, config);
                } else if (button[0].className.indexOf('buttons-excel') >= 0) {
                    $.fn.dataTable.ext.buttons.excelHtml5.available(dt, config) ?
                        $.fn.dataTable.ext.buttons.excelHtml5.action.call(self, e, dt, button, config) :
                        $.fn.dataTable.ext.buttons.excelFlash.action.call(self, e, dt, button, config);
                } else if (button[0].className.indexOf('buttons-csv') >= 0) {
                    $.fn.dataTable.ext.buttons.csvHtml5.available(dt, config) ?
                        $.fn.dataTable.ext.buttons.csvHtml5.action.call(self, e, dt, button, config) :
                        $.fn.dataTable.ext.buttons.csvFlash.action.call(self, e, dt, button, config);
                } else if (button[0].className.indexOf('buttons-pdf') >= 0) {
                    $.fn.dataTable.ext.buttons.pdfHtml5.available(dt, config) ?
                        $.fn.dataTable.ext.buttons.pdfHtml5.action.call(self, e, dt, button, config) :
                        $.fn.dataTable.ext.buttons.pdfFlash.action.call(self, e, dt, button, config);
                } else if (button[0].className.indexOf('buttons-print') >= 0) {
                    $.fn.dataTable.ext.buttons.print.action(e, dt, button, config);
                }
            }

            // Trigger export
            if (allDataRequested) {
                dt.ajax.reload(); // Reload the full data
            }
        }

    // Function to add search inputs to the specified DataTable
    function addFilterInputs(tableId) {
        if ($(`${tableId} thead tr#selectFilter`).length === 0) {
            // Only add filters if not already present
            $(`${tableId} thead tr`).clone(true).appendTo(`${tableId} thead`);
            $(`${tableId} thead tr:eq(1)`).attr('id', 'selectFilter');
        }

        $(`${tableId} thead tr:eq(1) th`).each(function (i) {
            var title = $(this).text();
            var input = $('<input type="text" placeholder="Search ' + title + '" style="width: -webkit-fill-available" />');
            $(this).html(input);

            input.on('keyup change', function () {
                    var columnIndex = $(this).parent().index();
                    var searchValue = this.value.trim(); // Trim whitespace
                    if (searchValue === '') {
                        // Clear search if empty
                        tableId.column(columnIndex).search('').draw();
                        return;
                    }
                    // Escape the entire search value for exact phrase matching
                    var escapedValue = $.fn.dataTable.util.escapeRegex(searchValue);
                    // Use regex to search for the exact phrase (case insensitive)
                    tableId.column(columnIndex).search(escapedValue, true, false).draw();
                });
        });
    }

    // Apply filter functionality to the table
    addFilterInputs('#dt-product-titles-1');
    addFilterInputs('#dt-product-titles-2');
    addFilterInputs('#dt-product-titles-3');
    addFilterInputs('#dt-product-titles-4');
    addFilterInputs('#dt-product-titles-5');
    addFilterInputs('#dt-product-titles-6');
    addFilterInputs('#dt-product-titles-7');
    addFilterInputs('#dt-product-titles-8');
    addFilterInputs('#dt-product-titles-9');
    addFilterInputs('#dt-product-titles-10');
    addFilterInputs('#dt-product-titles-11');
    addFilterInputs('#dt-product-titles-12');
    addFilterInputs('#dt-product-titles-13');
    addFilterInputs('#dt-product-titles-14');

    // Editor for DataTable
    var editor = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-1",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            { label: "", name: "gec_item_content.pt_gec_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_variation_product_title" },
            { label: "", name: "gec_item_content.pt_client_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_variation_product_title" },
            { label: "", name: "gec_retailer_homedepot_idm.Product_Name" },
            { label: "", name: "gec_item_content.pt_home_depot_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							]
						 },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_bbb_overstock_product_title" },
            { label: "", name: "gec_item_content.pt_bbb_overstock_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_target_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_target_variation_product_title" },
            { label: "", name: "gec_retailer_targ_launchpad.Product_Title" },
            { label: "", name: "gec_item_content.pt_target_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_walmart_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_walmart_variation_product_title" },
            { label: "", name: "gec_retailer_walm_rl_item.Product_Name" },
            { label: "", name: "gec_item_content.pt_walmart_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_amazon_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_amazon_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_amazon_vc_product_title" },
            { label: "", name: "gec_item_content.pt_amazon_vc_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_wayfair_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_wayfair_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_wayfair_product_title" },
            { label: "", name: "gec_item_content.pt_wayfair_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_live_amazon_sc_product_title" },
            { label: "", name: "gec_item_content.pt_amazon_sc_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_houzz_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_houzz_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_houzz_product_title" },
            { label: "", name: "gec_item_content.pt_houzz_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] },
            { label: "", name: "gec_item_content.pt_gec_home_depot_ca_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_ca_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_home_depot_ca_product_title" },
            { label: "", name: "gec_item_content.pt_home_depot_ca_product_title_status",
							type: "select",
							options: [
									{ label: "---", value: "" },
									{ label: "Correct", value: "Correct" },
									{ label: "Submitted", value: "Submitted" },
									{ label: "Update Needed", value: "Update Needed" }
							] }
        ],
    });

		// Editor for DataTable2,
    var editor2 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-2",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            { label: "", name: "gec_item_content.pt_gec_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_variation_product_title" },
						{ label: "", name: "gec_item_content.pt_client_product_title"} // client provided product title
        ],
    });

		// Editor for DataTable3
    var editor3 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-3",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
						//{ label: "", name: "gec_item_content."}, //GEC Product Title
            { label: "", name: "gec_item_content.pt_gec_home_depot_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_single_product_title" },
						{ label: "", name: "gec_item_content.pt_gec_target_single_product_title"},
						{ label: "", name: "gec_item_content.pt_gec_walmart_single_product_title"},
						{ label: "", name: "gec_item_content.pt_gec_amazon_single_product_title"}, //GEC Amazon Single Product Title
						{ label: "", name: "gec_item_content.pt_gec_wayfair_single_product_title"},
						//{ label: "", name: "gec_item_content."}, //GEC Amazon SC Single Product Title
						{ label: "", name: "gec_item_content.pt_gec_houzz_single_product_title"},
						{ label: "", name: "gec_item_content.pt_gec_home_depot_ca_single_product_title"}
        ],
    });

		// Editor for DataTable4,
    var editor4 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-4",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            { label: "", name: "gec_item_content.pt_gec_home_depot_variation_product_title" },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_variation_product_title" },
            { label: "", name: "gec_item_content.pt_gec_target_variation_product_title" },
            { label: "", name: "gec_item_content.pt_gec_walmart_variation_product_title" },
            { label: "", name: "gec_item_content.pt_gec_amazon_variation_product_title" }, //GEC Amazon VC Variation Product Title
            { label: "", name: "gec_item_content.pt_gec_wayfair_variation_product_title" },
            //{ label: "", name: "gec_item_content." }, //GEC Amazon SC Variation Product Title
            { label: "", name: "gec_item_content.pt_gec_houzz_variation_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_ca_variation_product_title" }
        ],
    });

		// Editor for DataTable5,
    var editor5 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-5",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            { label: "", name: "gec_retailer_homedepot_idm.Product_Name" },
            { label: "", name: "gec_item_content.pt_live_bbb_overstock_product_title" },
            { label: "", name: "gec_retailer_targ_launchpad.Product_Title" },
            { label: "", name: "gec_retailer_walm_rl_item.Product_Name" },
            { label: "", name: "gec_item_content.pt_live_amazon_vc_product_title" },
            { label: "", name: "gec_item_content.pt_live_wayfair_product_title" },
            { label: "", name: "gec_item_content.pt_live_amazon_sc_product_title" },
            { label: "", name: "gec_item_content.pt_live_houzz_product_title" },
            { label: "", name: "gec_item_content.pt_live_home_depot_ca_product_title" }
        ],
    });

    		// Editor for DataTable6,
    var editor6 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-6",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_variation_product_title" },
            { label: "", name: "gec_retailer_homedepot_idm.Product_Name" },
            { label: "", name: "gec_item_content.pt_home_depot_product_title_status" }
        ],
    });

    // Editor for DataTable7,
    var editor7 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-7",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_bbb_overstock_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_bbb_overstock_product_title" },
            { label: "", name: "gec_item_content.pt_bbb_overstock_product_title_status" }
        ],
    });

    // Editor for DataTable8,
    var editor8 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-8",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_target_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_target_variation_product_title" },
            { label: "", name: "gec_retailer_targ_launchpad.Product_Title" },
            { label: "", name: "gec_item_content.pt_target_product_title_status" }
        ],
    });

    // Editor for DataTable9,
    var editor9 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-9",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_walmart_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_walmart_variation_product_title" },
            { label: "", name: "gec_retailer_walm_rl_item.Product_Name" },
            { label: "", name: "gec_item_content.pt_walmart_product_title_status" }
        ],
    });

    // Editor for DataTable10,
    var editor10 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-10",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_amazon_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_amazon_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_amazon_vc_product_title" },
            { label: "", name: "gec_item_content.pt_amazon_vc_product_title_status" }
        ],
    });

    // Editor for DataTable11,
    var editor11 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-11",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_wayfair_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_wayfair_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_wayfair_product_title" },
            { label: "", name: "gec_item_content.pt_wayfair_product_title_status" }
        ],
    });

    // Editor for DataTable12,
    var editor12 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-12",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_amazon_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_amazon_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_amazon_sc_product_title" },
            { label: "", name: "gec_item_content.pt_amazon_sc_product_title_status" }
        ],
    });

        // Editor for DataTable13,
    var editor13 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-13",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_houzz_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_houzz_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_houzz_product_title" },
            { label: "", name: "gec_item_content.pt_houzz_product_title_status" }
        ],
    });

            // Editor for DataTable14,
    var editor14 = new $.fn.dataTable.Editor({
        ajax: "controllers/product_titles.php",
        table: "#dt-product-titles-14",
        fields: [
            { label: "", name: "gec_item_content.gecid" },
            //{ label: "", name: "gec_item_content." }, //GEC Product Title
            // { label: "", name: "gec_item.thd_omsid" },
            // { label: "", name: "gec_item.thd_store_sku" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_ca_single_product_title" },
            { label: "", name: "gec_item_content.pt_gec_home_depot_ca_variation_product_title" },
            { label: "", name: "gec_item_content.pt_live_home_depot_ca_product_title" },
            { label: "", name: "gec_item_content.pt_home_depot_ca_product_title_status" }
        ],
    });

    // Function to render icons in cells
    var combinedRender = function (data, type, row, meta) {
        if (type === 'display') {
            return (data ? data : "") +
								<?php if (in_array('w', $page_permissions)) : ?>
                    ' <i class="fa fa-pencil pencil-icon mr-3"></i> '
                    <?php endif; ?>
                '<i class="fa fa-history history-icon"></i>';
        }
        return data;
    };

    // Function to initialize DataTable
    function initializeDataTable(selector, columns) {
        return $(selector).DataTable({
            dom: "lBfrtip",
            ajax: {
                url: "controllers/product_titles.php",
                type: "POST",
                data: function (a) {
                    a.clientFilter = $('#initial_selectClient').val();
                    a.statusFilter = $('#initial_select1').val();
                    a.variationTypeFilter = $('#initial_select2').val();
                    a.retailerManagedFilter = $('#initial_select3').val();

										// Pass URL parameter to AJAX requests
										const urlParams = new URLSearchParams(window.location.search);
										if (urlParams.get('select1')) {
														a.select1 = urlParams.get('select1');
                        }
												if (urlParams.get('instock')) {
														a.instock = urlParams.get('instock');
                        }
                },
            },
            deferRender: true,
            orderCellsTop: true,
            responsive: true,
            fixedHeader: true,
            colReorder: true,
            stateSave: true,
            serverSide: true,
            processing: true,
            scrollX: true,
            scrollCollapse: true,
            lengthMenu: [15, 25, 50, 75, 100, -1],
            columns: columns,
            select: true,
            buttons: [
                { extend: 'colvis', text: 'Column Visibility', className: 'btn-outline-default float-start' },
                <?php echo in_array('e', $page_permissions) ? "
                    { extend: 'collection', text: 'Export',
                        buttons: [
                            { extend: 'copy', text: 'Copy',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                action: newexportaction // Call custom export action for copy button
                            },
                            { extend: 'excel', text: 'Excel',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                action: newexportaction // Call custom export action for excel button
                            },
                            { extend: 'csv', text: 'CSV',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                action: newexportaction // Call custom export action for csv button
                            },
                            { extend: 'pdf', text: 'PDF',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                action: newexportaction // Call custom export action for pdf button
                            },
                            // { extend: 'print', text: 'Print',
                            //     exportOptions: {
                            //         columns: ':visible'
                            //     },
                            //     action: newexportaction // Call custom export action for print button
                            // }
                        ],
                        select: true // Enable row selection
                    }" : "";
                ?>
            ],
        });
    }

    // Define columns
    var column1 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
				{ data: "gec_item_content.variations_variation_type"},
        { data: "gec_item_content.pt_gec_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_client_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_homedepot_idm.Product_Name", className: "text-left" },
        { data: "gec_item_content.pt_home_depot_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_bbb_overstock_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_bbb_overstock_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_bbb_overstock_product_title", className: "text-left" },
        { data: "gec_item_content.pt_bbb_overstock_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_target_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_target_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_targ_launchpad.Product_Title", className: "text-left"},
        { data: "gec_item_content.pt_target_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_walmart_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_walmart_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_walm_rl_item.Product_Name", className: "text-left"},
        { data: "gec_item_content.pt_walmart_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_amazon_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_amazon_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_amazon_vc_product_title", className: "text-left"},
        { data: "gec_item_content.pt_amazon_vc_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_wayfair_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_wayfair_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_wayfair_product_title", className: "text-left"},
        { data: "gec_item_content.pt_wayfair_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_amazon_sc_product_title", className: "text-left"},
        { data: "gec_item_content.pt_amazon_sc_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_houzz_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_houzz_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_houzz_product_title", className: "text-left" },
        { data: "gec_item_content.pt_houzz_product_title_status", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_ca_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_ca_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_home_depot_ca_product_title", className: "text-left" },
        { data: "gec_item_content.pt_home_depot_ca_product_title_status", className: "text-left", render: combinedRender }
    ];

		// Define columns 2
    var column2 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
				{ data: "gec_item_content.variations_variation_type"},
        { data: "gec_item_content.pt_gec_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_client_product_title", className: "text-left", render: combinedRender }
    ];

		// Define columns 3
    var column3 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
				{ data: null},
				{ data: "gec_item_content.pt_gec_home_depot_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_bbb_overstock_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_target_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_walmart_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_amazon_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_wayfair_single_product_title", className: "text-left", render: combinedRender},
				// { data: null},
				{ data: "gec_item_content.pt_gec_houzz_single_product_title", className: "text-left", render: combinedRender},
				{ data: "gec_item_content.pt_gec_home_depot_ca_single_product_title", className: "text-left", render: combinedRender }
    ];

		// Define columns 4
    var column4 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: null },
        { data: "gec_item_content.pt_gec_home_depot_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_bbb_overstock_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_target_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_walmart_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_amazon_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_wayfair_variation_product_title", className: "text-left", render: combinedRender },
       // { data: null },
        { data: "gec_item_content.pt_gec_houzz_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_ca_variation_product_title", className: "text-left", render: combinedRender }
    ];

		// Define columns 5
    var column5 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: null }, //GEC Product Title
        { data: "gec_retailer_homedepot_idm.Product_Name", className: "text-left"},
        { data: "gec_item_content.pt_live_bbb_overstock_product_title", className: "text-left"},
        { data: "gec_retailer_targ_launchpad.Product_Title", className: "text-left"},
        { data: "gec_retailer_walm_rl_item.Product_Name", className: "text-left" },
        { data: "gec_item_content.pt_live_amazon_vc_product_title", className: "text-left"},
        { data: "gec_item_content.pt_live_wayfair_product_title", className: "text-left"},
        { data: "gec_item_content.pt_live_amazon_sc_product_title", className: "text-left" },
        { data: "gec_item_content.pt_live_houzz_product_title", className: "text-left" },
        { data: "gec_item_content.pt_live_home_depot_ca_product_title", className: "text-left" }
    ];

    		// Define columns 6
    var column6 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.thd_omsid", className: "text-left" },
        { data: "gec_item.thd_store_sku", className: "text-left" },
        { data: "gec_item_content.pt_gec_home_depot_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_homedepot_idm.Product_Name", className: "text-left"},
        { data: "gec_item_content.pt_home_depot_product_title_status", className: "text-left", render: combinedRender }
    ];

        		// Define columns 7
    var column7 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.bbb_ovsk_sku", className: "text-left" },
        { data: "gec_item.bbb_ovsk_product_id", className: "text-left" },
        { data: "gec_item_content.pt_gec_bbb_overstock_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_bbb_overstock_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_bbb_overstock_product_title", className: "text-left"}, 
        { data: "gec_item_content.pt_bbb_overstock_product_title_status", className: "text-left", render: combinedRender }
    ];

    // Define columns 8
    var column8 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.trgt_tcin", className: "text-left" },
        { data: "gec_item.trgt_parent_tcin", className: "text-left" },
        { data: "gec_item.trgt_dpci", className: "text-left" },
        { data: "gec_item_content.pt_gec_target_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_target_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_targ_launchpad.Product_Title", className: "text-left" },
        { data: "gec_item_content.pt_target_product_title_status", className: "text-left", render: combinedRender }
    ];

        // Define columns 9
    var column9 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.wmrl_wm_number", className: "text-left" },
        { data: "gec_item_content.pt_gec_walmart_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_walmart_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_retailer_walm_rl_item.Product_Name", className: "text-left"},
        { data: "gec_item_content.pt_walmart_product_title_status", className: "text-left", render: combinedRender }
    ];

            // Define columns 10
    var column10 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.amazon_asin", className: "text-left" },
        { data: "gec_item_content.pt_gec_amazon_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_amazon_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_amazon_vc_product_title", className: "text-left"},
        { data: "gec_item_content.pt_amazon_vc_product_title_status", className: "text-left", render: combinedRender }
    ];

                // Define columns 11
    var column11 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.wayf_sku", className: "text-left" },
        { data: "gec_item_content.pt_gec_wayfair_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_wayfair_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_wayfair_product_title", className: "text-left" },
        { data: "gec_item_content.pt_wayfair_product_title_status", className: "text-left", render: combinedRender }
    ];

                    // Define columns 12
    var column12 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.amazon_asin", className: "text-left" },
        { data: "gec_item_content.pt_gec_amazon_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_amazon_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_amazon_sc_product_title", className: "text-left"},
        { data: "gec_item_content.pt_amazon_sc_product_title_status", className: "text-left", render: combinedRender }
    ];

                        // Define columns 13
    var column13 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.houzz_product_id", className: "text-left" },
        { data: "gec_item_content.pt_gec_houzz_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_houzz_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_houzz_product_title", className: "text-left" },
        { data: "gec_item_content.pt_houzz_product_title_status", className: "text-left", render: combinedRender }
    ];

        		// Define columns 14
    var column14 = [
        { data: "gec_item_content.gecid", className: "text-left" },
        { data: "gec_item.item", className: "text-left" },
        { data: "gec_item.thd_omsid", className: "text-left" },
        { data: "gec_item.thd_store_sku", className: "text-left" },
        { data: "gec_item_content.pt_gec_home_depot_ca_single_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_gec_home_depot_ca_variation_product_title", className: "text-left", render: combinedRender },
        { data: "gec_item_content.pt_live_home_depot_ca_product_title", className: "text-left" },
        { data: "gec_item_content.pt_home_depot_ca_product_title_status", className: "text-left", render: combinedRender }
    ];

    // Initialize DataTable
    dataTable = initializeDataTable('#dt-product-titles-1', column1);
    dataTable2 = initializeDataTable('#dt-product-titles-2', column2);
    dataTable3 = initializeDataTable('#dt-product-titles-3', column3);
    dataTable4 = initializeDataTable('#dt-product-titles-4', column4);
    dataTable5 = initializeDataTable('#dt-product-titles-5', column5);
    dataTable6 = initializeDataTable('#dt-product-titles-6', column6);
    dataTable7 = initializeDataTable('#dt-product-titles-7', column7);
    dataTable8 = initializeDataTable('#dt-product-titles-8', column8);
    dataTable9 = initializeDataTable('#dt-product-titles-9', column9);
    dataTable10 = initializeDataTable('#dt-product-titles-10', column10);
    dataTable11 = initializeDataTable('#dt-product-titles-11', column11);
    dataTable12 = initializeDataTable('#dt-product-titles-12', column12);
    dataTable13 = initializeDataTable('#dt-product-titles-13', column13);
    dataTable14 = initializeDataTable('#dt-product-titles-14', column14);


   // Handle pencil-icon clicks for editing
$('#dt-product-titles-1 tbody, #dt-product-titles-2 tbody, #dt-product-titles-3, #dt-product-titles-4, #dt-product-titles-5, #dt-product-titles-6, #dt-product-titles-7, #dt-product-titles-8, #dt-product-titles-9, #dt-product-titles-10, #dt-product-titles-11, #dt-product-titles-12, #dt-product-titles-13, #dt-product-titles-14 tbody').on('click', 'td i.pencil-icon', function (e) {
    e.stopImmediatePropagation();

    // Determine which table the clicked icon belongs to
    var tableId = $(this).closest('table').attr('id');

    // Use the appropriate editor for inline editing
    if (tableId === 'dt-product-titles-1') {
        editor.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-2') {
        editor2.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-3') {
        editor3.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-4') {
        editor4.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-5') {
        editor5.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-6') {
        editor6.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-7') {
        editor7.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-8') {
        editor8.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-9') {
        editor9.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-10') {
        editor10.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-11') {
        editor11.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-12') {
        editor12.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-13') {
        editor13.inline($(this).closest('td'), { onBlur: 'submit' });
    } else if (tableId === 'dt-product-titles-14') {
        editor14.inline($(this).closest('td'), { onBlur: 'submit' });
    }
});

    // Handle history-icon clicks for showing modal
    $('#dt-product-titles-1, #dt-product-titles-2, #dt-product-titles-3, #dt-product-titles-4, #dt-product-titles-5, #dt-product-titles-6, #dt-product-titles-7, #dt-product-titles-8, #dt-product-titles-9, #dt-product-titles-10, #dt-product-titles-11, #dt-product-titles-12, #dt-product-titles-13, #dt-product-titles-14 tbody').on('click', 'td i.history-icon', function (e) {
        e.stopImmediatePropagation();

        var $table = $(this).closest('table');
        var activeDataTable =
				$table.attr('id') === 'dt-product-titles-1' ? dataTable : 
				($table.attr('id') === 'dt-product-titles-2' ? dataTable2 : 
				 $table.attr('id') === 'dt-product-titles-3' ? dataTable3 :
				 $table.attr('id') === 'dt-product-titles-4' ? dataTable4 :
				 $table.attr('id') === 'dt-product-titles-5' ? dataTable5 : 
                 $table.attr('id') === 'dt-product-titles-6' ? dataTable6 : 
                 $table.attr('id') === 'dt-product-titles-7' ? dataTable7 :
                 $table.attr('id') === 'dt-product-titles-8' ? dataTable8 :
                 $table.attr('id') === 'dt-product-titles-9' ? dataTable9 :
                 $table.attr('id') === 'dt-product-titles-10' ? dataTable10 :
                 $table.attr('id') === 'dt-product-titles-11' ? dataTable11 :
                 $table.attr('id') === 'dt-product-titles-12' ? dataTable12 :
                 $table.attr('id') === 'dt-product-titles-13' ? dataTable13 :
                ($table.attr('id') === 'dt-product-titles-14' ? dataTable14 : null));

        var $td = $(this).closest('td');
        var colIndex = activeDataTable.cell($td).index().column;
        var edited_column = activeDataTable.column(colIndex).header().innerText;
        var rowData = activeDataTable.row($(this).closest('tr')).data();

        showHistoryModal(rowData.gec_item_content.gecid, edited_column);
    });

    // Function to show the history modal
    function showHistoryModal(gecid, edited_column) {
        $('#ModalColumnIdentifier').text(gecid);
        $('#ModalColumnTitle').text(edited_column);
        $.ajax({
            url: 'product_titles_code.php',
            type: 'POST',
            data: { gecid: gecid, edited_column: edited_column },
            success: function (response) {
                $('#historyModalBody').html(response);
                $('#historyModal').modal('show');
            },
        });
    }

    // Reload DataTables on filter change
    $('#initial_selectClient, #initial_select1, #initial_select2, #initial_select3').on('change', function () {
        dataTable.ajax.reload();
				dataTable2.ajax.reload();
				dataTable3.ajax.reload();
				dataTable4.ajax.reload();
				dataTable5.ajax.reload();
                dataTable6.ajax.reload();
                dataTable7.ajax.reload();
                dataTable8.ajax.reload();
                dataTable9.ajax.reload();
                dataTable10.ajax.reload();
                dataTable11.ajax.reload();
                dataTable12.ajax.reload();
                dataTable13.ajax.reload();
                dataTable14.ajax.reload();
    });

    // Reset column dragging
    $('#resetColReorderBtn').on('click', function () {
        dataTable.colReorder.reset();
				dataTable2.colReorder.reset();
				dataTable3.colReorder.reset();
				dataTable4.colReorder.reset();
				dataTable5.colReorder.reset();
                dataTable6.colReorder.reset();
                dataTable7.colReorder.reset();
                dataTable8.colReorder.reset();
                dataTable9.colReorder.reset();
                dataTable10.colReorder.reset();
                dataTable11.colReorder.reset();
                dataTable12.colReorder.reset();
                dataTable13.colReorder.reset();
                dataTable14.colReorder.reset();

    });

    // Handle tab switch
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        if (target === '#tab-alldata') {
            dataTable.ajax.reload();
        } else if(target === '#tab-default') {
					dataTable2.ajax.reload();
				} else if(target === '#tab-product-titles-3') {
					dataTable3.ajax.reload();
				} else if(target === '#tab-product-titles-4') {
					dataTable4.ajax.reload();
				} else if(target === '#tab-product-titles-5') {
					dataTable5.ajax.reload();
				} else if(target === '#tab-product-titles-6') {
					dataTable6.ajax.reload();
				} else if(target === '#tab-product-titles-7') {
					dataTable7.ajax.reload();
				} else if(target === '#tab-product-titles-8') {
					dataTable8.ajax.reload();
				} else if(target === '#tab-product-titles-9') {
					dataTable9.ajax.reload();
				} else if(target === '#tab-product-titles-10') {
					dataTable10.ajax.reload();
				} else if(target === '#tab-product-titles-11') {
					dataTable11.ajax.reload();
				} else if(target === '#tab-product-titles-12') {
					dataTable12.ajax.reload();
				} else if(target === '#tab-product-titles-13') {
					dataTable13.ajax.reload();
				} else if(target === '#tab-product-titles-14') {
					dataTable14.ajax.reload();
				}
    });

		$('#view-data-options').on('change', function () {
    // List of valid tab IDs
    const validTabs = [
        '#tab-alldata',
        '#tab-default',
        '#tab-product-titles-2',
        '#tab-product-titles-3',
        '#tab-product-titles-4',
        '#tab-product-titles-5',
        '#tab-product-titles-6',
        '#tab-product-titles-7',
        '#tab-product-titles-8',
        '#tab-product-titles-9',
        '#tab-product-titles-10',
        '#tab-product-titles-11',
        '#tab-product-titles-12',
        '#tab-product-titles-13',
        '#tab-product-titles-14'
    ];

    // Check if the selected value is in the validTabs array
    if (validTabs.includes($(this).val())) {
        setTimeout(function () {
            if (typeof dataTable !== 'undefined') dataTable.ajax.reload();
            if (typeof dataTable2 !== 'undefined') dataTable2.ajax.reload();
            if (typeof dataTable3 !== 'undefined') dataTable3.ajax.reload();
            if (typeof dataTable4 !== 'undefined') dataTable4.ajax.reload();
            if (typeof dataTable5 !== 'undefined') dataTable5.ajax.reload();
            if (typeof dataTable6 !== 'undefined') dataTable6.ajax.reload();
            if (typeof dataTable7 !== 'undefined') dataTable7.ajax.reload();
            if (typeof dataTable8 !== 'undefined') dataTable8.ajax.reload();
            if (typeof dataTable9 !== 'undefined') dataTable9.ajax.reload();
            if (typeof dataTable10 !== 'undefined') dataTable10.ajax.reload();
            if (typeof dataTable11 !== 'undefined') dataTable11.ajax.reload();
            if (typeof dataTable12 !== 'undefined') dataTable12.ajax.reload();
            if (typeof dataTable13 !== 'undefined') dataTable13.ajax.reload();
            if (typeof dataTable14 !== 'undefined') dataTable14.ajax.reload();
        }, 100);
    }
});
			
});
</script>

</div>
</div>


												</div>
								</div>
						</div>
				</div>
		</div>
</div>


						</main>
                    <!-- this overlay is activated only when mobile menu is triggered -->
                    <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div> <!-- END Page Content -->
                    <!-- BEGIN Page Footer -->
                        <?php include ('../includes/footer.php'); ?>
                    <!-- END Page Footer -->
                    <!-- BEGIN Shortcuts -->
                        <?php include ('../includes/shortcuts.php'); ?>
                    <!-- END Shortcuts -->
                    <!-- BEGIN Color profile -->
                        <?php include ('../includes/color_profile.php'); ?>
                    <!-- END Color profile -->
                </div>
            </div>
        </div>
        <!-- END Page Wrapper -->
        <!-- BEGIN Quick Menu -->
        <!-- to add more items, please make sure to change the variable '$menu-items: number;' in your _page-components-shortcut.scss -->
            <?php include ('../includes/quick_menu.php'); ?>
        <!-- END Quick Menu -->
        <!-- BEGIN Messenger -->
            <?php include ('../includes/messenger.php'); ?>
        <!-- END Messenger -->
        <!-- BEGIN Page Settings -->
            <?php include ('../includes/page_settings.php'); ?>
        <!-- END Page Settings -->
        <!-- BEGIN Bottom -->
            <?php include ('../includes/bottom.php') ?>
        <!-- END Bottom -->

				<script src="script.js"></script>
				<script src="<?=base_url?>assets/js/notifications/toastr/toastr.js"></script>

    </body>
    <!-- END Body -->

<!-- Modal Import -->
<div class="modal fade" id="modal_import_product_titles" data-backdrop="static" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content" id="step2Container">
					<form id="btn_import_product_titles_form" enctype="multipart/form-data" class="form-horizontal">
							<div class="modal-header">
									<h4 class="modal-title">Product Titles <span class="text-warning float-end" id="message_import_product_titles"></span></h4>
									<button type="button" class="close" id="btn_import_close_product_titles" data-dismiss="modal" aria-label="Close">
											<span aria-hidden="true"><i class="fal fa-times"></i></span>
									</button>
							</div>
							
							<div class="modal-body">
									<div class="p-3 mb-2 rounded border border-faded" style="background-color: #544e82;">
											<div class="form-group">
												
													<div class="container d-flex justify-content-center mt-100">
															<div class="row">
																	<div class="col-md-12" id="step5Container">
																			<!-- <input class="form-control" style="padding:0.35rem !important;" id="excel_import_inventory" type="file" name="file" id="file" placeholder="Drag and drop your file here" accept=".csv, .xlsx" required> -->
																			<div class="file-drop-area">
																					<span class="choose-file-button">Choose files</span>
																					<span class="file-message">or drag and drop files here <br>Acceptable formats:.CSV, .XLSX</span>
																					<input type="file" class="file-input" id="excel_import_product_titles" name="file" accept=".csv, .xlsx" required>
																			</div>
                                                                            <div id="progress-container" style="display: none; width: 100%; margin-top: 10px;">
                                                                                <div id="progress-bar" style="width: 0%; height: 20px; background-color: #4caf50; text-align: center; line-height: 20px; color: white;">
                                                                                    0%
                                                                                </div>
                                                                            </div>
                                                                            <p id="upload_status"></p>
																	</div>
															</div>
													</div>
											</div>
									</div>
							</div>
							<div class="modal-footer">
									<div class="form-group d-flex flex-row-reverse">
											<button type="submit" id="btn_import_product_titles" data-form-type="btn_import_product_titles" class="btn btn-primary bg-trans-gradient"><i class="fal fa-upload"></i> Upload </button>
											<button type="submit" id="btn_import_product_titles_template" data-form-type="btn_import_product_titles_template" class="btn btn-primary bg-trans-gradient mr-2"><i class="fal fa-download"></i> Download Template</button>
									</div>
							</div>
					</form>
			</div>
	</div>
</div>

<!-- Modal HTML -->
<div class="modal fade" id="historyModal" tabindex="-1" role="dialog" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="historyModalLabel">Edit History (<span id="ModalColumnTitle"></span>)</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <h5 class="modal-title" id="historyModalLabel" style="margin-left: 1.2rem !important;">GECID: <span id="ModalColumnIdentifier"></span></h5>
                <div class="modal-body overflow-auto" id="historyModalBody">
                    <!-- History content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Modal -->
    <div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-right modal-lg modal-transparent">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title h4 text-white">View Audit History</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div id="view-audit-logs-div">
                </div>
            </div>
        </div>
    </div>
    <!-- JavaScript for Modal Audit Logs -->
    <script>
        function view_audit_logsModal(button) {
            const xhrAjax = new XMLHttpRequest();
            xhrAjax.open("POST", "product_titles_code.php", true);
            xhrAjax.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhrAjax.onreadystatechange = function () {
                if (xhrAjax.readyState === 4 && xhrAjax.status === 200) {
                    var responseText = xhrAjax.responseText;
                    // Update the view-audit-logs-div div with the response
                    document.getElementById("view-audit-logs-div").innerHTML = responseText;
                }
            };
            xhrAjax.send(`Log_audit_request=${1}`);
        }
    </script>

    <!-- Logs Modal -->
		<div class="modal fade" id="logsModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">View Logs</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="panel-content">
                        <div class="border px-3 pt-3 pb-0 rounded">
                            <div class="tab-content py-3">
                                <!-- datatable start -->
                                <table id="dt-tableLogs" class="table table-bordered table-hover table-sm table-striped w-100 text-center">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Client</th>
                                            <th>Log Type</th>
                                            <th>File Name</th>
                                            <th>Status</th>
                                            <th>User</th>
                                            <th>Log Date</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <!-- datatable end -->
                                <script type="text/javascript">
                                    $(document).ready(function() {
                                        var dataTable = $('#dt-tableLogs').DataTable({
                                            ajax: {
                                                url:'product_titles_code.php',
                                                type: "POST",
                                                data: function (data) {
                                                    data.log_list = "1";
                                                    data.clientFilter = $('#initial_selectClient').val();
                                                }
                                            },
                                            bDeferRender: true, // Enable speed rendering
                                            orderCellsTop: true,
                                            responsive: true,
                                            fixedHeader: true,
                                            // paging: false,
                                            colReorder: true, // Enable column dragging
                                            // stateSave: true, // Enable state saving
                                            serverSide: true,
                                            processing: true,
                                            serverMethod: 'post',
                                            scrollX: true, // Enable horizontal scrolling
                                            scrollCollapse: true, // Allow vertical scrollbar when necessary
                                            lengthMenu: [ 15, 25, 50, 75, 100, -1 ],
                                            columns: [
                                                { data: 'client_name', className: 'text-left' },
                                                { data: 'Log_Type', className: 'text-left' },
                                                { data: 'Log_File_Name', className: 'text-left' },
                                                { data: 'Log_Status', className: 'text-left' },
                                                { data: 'Log_User', className: 'text-left' },
                                                { data: 'new_Log_Datetime', className: 'text-left' },
                                                { data: null,
                                                    render: function(data, type, row, meta) {
                                                            return '<div class="col-md-12">' +
                                                                '<button type="button" class="btn btn-sm btn-outline-primary btn-icon btn-inline-block mr-1" data-toggle="modal" data-target=".view_report_logs" data-Log_ID="' + data.Log_ID + '" data-file_name="' + data.Log_File_Name + '" onclick="view_report_logsModal(this)" title="View Report Logs"><i class="fal fa-eye"></i></button>' +
                                                                '</div>';
                                                    },
                                                    orderable: false
                                                }
                                            ],
                                            order: [[5, 'desc']],
                                            select: true,
                                        });

                                        $('#initial_selectClient').on('change', function() {
                                            dataTable.ajax.reload(); // Reload DataTable when status filter changes
                                        });

                                        // Add click event listener to the "Log History" button
                                        $('#logHistory_btn').on('click', function() {
                                            setTimeout(function() {
                                                dataTable.ajax.reload(); // Reload DataTable after 1.5 seconds delay
                                            }, 500);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <!-- Modal View Logs-->
		<div class="modal fade view_report_logs" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-right modal-lg modal-transparent">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title h4 text-white">View Report Logs (<span id="file_title"></span>)</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div id="view-report-logs-div">
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Modal View Logs -->
		<script>
        function view_report_logsModal(button) {
            var Log_ID = button.getAttribute("data-Log_ID");
            document.getElementById("file_title").innerHTML = button.getAttribute("data-file_name");

            const xhrAjax = new XMLHttpRequest();
            xhrAjax.open("POST", "product_titles_code.php", true);
            xhrAjax.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            xhrAjax.onreadystatechange = function () {
                if (xhrAjax.readyState === 4 && xhrAjax.status === 200) {
                    var responseText = xhrAjax.responseText;
                    // Update the view-report-logs-div div with the response
                    document.getElementById("view-report-logs-div").innerHTML = responseText;
                }
            };
            xhrAjax.send(`Log_ID=${Log_ID}`);
        }
    </script>

    <script> // IMPORT AND EXPORT

        $(document).ready(function() {

		// Script for Import
		//datatable 1
		if (!$.fn.DataTable.isDataTable('#dt-product-titles-1')) {
				var dataTable = $('#dt-product-titles-1').DataTable({
						
				});
		} else {
				var dataTable = $('#dt-product-titles-1').DataTable(); 
		}

		if (typeof dataTable === 'undefined') {
		console.error('dataTable is not defined');
		return;
    }
	
        <?php if (in_array('i', $page_permissions)) : ?>

            // Script for auto upload file with progress bar
            let uploadedFilePaths = "";
            let isFileUploaded = false; // Track if the user manually uploaded the file
            let ajaxUploaded = false;

            $('#excel_import_product_titles').on('change', function () {
                var files = this.files;
                if (!files.length) return;

                var formData = new FormData();
                for (var i = 0; i < files.length; i++) {
                    formData.append('file[]', files[i]);
                }
                formData.append('auto_upload', '1');

                // Delete previous uploads if not manual and not ajax
                if (uploadedFilePaths.length > 0 && !isFileUploaded && !ajaxUploaded) {
                    uploadedFilePaths.forEach(function (filePath) {
                        $.ajax({
                            url: "product_titles_code.php",
                            method: "POST",
                            data: { delete_file: true, file_path: filePath },
                            cache: false
                        });
                    });
                    uploadedFilePaths = []; // Clear the array after deletion
                }

                $.ajax({
                    url: "product_titles_code.php",
                    method: "POST",
                    data: formData,
                    contentType: false,
                    cache: false,
                    processData: false,
                    beforeSend: function () {
                        $('#upload_status').html('<span class="text-white">Uploading...</span>');
                        $('#progress-container').show();
                        $('#progress-bar').css('width', '0%').text('0%');
                        $('#btn_import_close, #excel_import_product_titles, #btn_import, #btn_import_item_status_template').prop('disabled', true).addClass('disabled');
                    },
                    xhr: function () {
                        var xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                $('#progress-bar').css('width', percentComplete + '%').text(percentComplete + '%');
                            }
                        }, false);
                        return xhr;
                    },
                    success: function (response) {
                        response = JSON.parse(response);
                        if (response.status == "success") {
                            $('#upload_status').html('<span class="text-success">Files uploaded successfully.</span>');
                            uploadedFilePaths = response.files.map(file => file.file_path);
                            ajaxUploaded = false;
                            $('#excel_import_product_titles').attr('data-file-paths', JSON.stringify(uploadedFilePaths));
                        } else {
                            let errorMessage = 'Upload failed: ';
                            if (response.errors) {
                                response.errors.forEach(error => {
                                    errorMessage += error.file_name + ': ' + error.message + '; ';
                                });
                            } else {
                                errorMessage += response.message;
                            }
                            $('#upload_status').html('<span class="text-danger">' + errorMessage + '</span>');
                            $('#btn_import_close, #excel_import_product_titles, #btn_import, #btn_import_item_status_template').removeAttr('disabled').removeClass('disabled');
                        }
                    },
                    error: function () {
                        $('#upload_status').html('<span class="text-danger">Error uploading files.</span>');
                        $('#btn_import_close, #excel_import_product_titles, #btn_import, #btn_import_item_status_template').removeAttr('disabled').removeClass('disabled');
                    },
                    complete: function () {
                        setTimeout(function () {
                            $('#progress-container').fadeOut();
                            $('#btn_import_close, #excel_import_product_titles, #btn_import, #btn_import_item_status_template').removeAttr('disabled').removeClass('disabled');
                        }, 1000);
                    }
                });
            });

            // Then use dataTable.ajax.reload() after import
            $('#btn_import_product_titles_form').on('submit', function(event) {
                $('#message_import_product_titles, #upload_status').html('');
                event.preventDefault();
                var formData = new FormData(this);
                formData.append('btn_product_titles_import', '1');

                // Get uploaded file path from input attribute
                var uploadedFilePaths = JSON.parse($('#excel_import_product_titles').attr('data-file-paths') || '[]');
                if (uploadedFilePaths.length > 0) {
                    formData.append('uploaded_file_paths', JSON.stringify(uploadedFilePaths));
                } else {
                    alert("Please upload files first.");
                    return;
                }  

                if ($('#initial_selectClient').val() == 'all_clients' || $('#initial_selectClient').val() == null) {
                    swal({
                        title: "Warning",
                        text: "Please select a client; import cannot be for all clients.",
                        icon: "warning",
                        button: true
                    }).then(function () {
                        $('#message_import_product_titles').text('');
                        dataTable.ajax.reload(); // Reload DataTable
                    });
                } else {
                    $.ajax({
                        url: "product_titles_code.php",
                        method: "POST",
                        data: formData,
                        dataType: "json",
                        contentType: false,
                        cache: false,
                        processData: false,
                        beforeSend: function() {
                            $('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', true).addClass('disabled');
                            $('#message_import_product_titles').text('Importing...');
                        },
                        success: function(data) {
                            swal({
                                title: data.title,
                                text: data.message,
                                icon: data.type,
                                button: true,
                                closeOnClickOutside: false
                            }).then(function () {
                                $('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', false).removeClass('disabled');
                                $('.file-message').html('or drag and drop files here <br>Acceptable formats:.CSV, .XLSX');
                                $('#message_import_product_titles').text('');
                                dataTable.ajax.reload(); // Reload DataTable only after success
                            });
                        },
                        error: function(jqXHR, textStatus) {
                            var output;
                                if (textStatus === "timeout") {
                                    output = { "title": "Server Error", "message": "Request timed out. Please try again", "type": "error" };
                                } else {
                                output = { "title": "File Error", "message": "The File is empty. Please reload and try again.", "type": "error" };
                            }
                            swal({
                                title: output.title,
                                text: output.message,
                                icon: output.type,
                                button: true
                            }).then(function () {
                                $('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', false).removeClass('disabled');
                                $('.file-message').html('or drag and drop files here <br>Acceptable formats:.CSV, .XLSX');
                                $('#message_import_product_titles').text('');
                                dataTable.ajax.reload(); // Reload DataTable after error handling
                            });
                        }
                    });
                }
            });
            <?php endif; ?>

            // Script for Export Template
            $('#btn_import_product_titles_template').on('click', function(){
                    $('#message_import_gecitem').html('');
                    var requestData = {
					btn_import_product_titles_template: '1'
                    };
                    
                    $.ajax({
                        url: "product_titles_code.php",
                        method: "POST",
                        data: requestData,
                        xhrFields: {
                            responseType: 'blob' // Set response type to blob
                        },
                        beforeSend: function(){
                            // Disable UI elements and show loading message
                            $('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', true).addClass('disabled');
                        $('#message_import_product_titles').text('Downloading template...');
                        },
                        success: function(data, textStatus, xhr){
                            // Extract filename from Content-Disposition header
                            var filename = "";
                            var contentDisposition = xhr.getResponseHeader('Content-Disposition');
                            if(contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                                var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                                var matches = filenameRegex.exec(contentDisposition);
                                if (matches != null && matches[1]) { 
                                    filename = matches[1].replace(/['"]/g, '');
                                }
                            }
                            
                            // Handle the file download
                            var blob = new Blob([data]);
                            var link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = filename || "Error.xlsx"; // Use filename from response headers, fallback to default filename
                            link.click();
                            
                            // Reset UI elements after file download
                            $('#btn_import_product_titles_form')[0].reset();
							$('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', false).removeClass('disabled');
							$('#message_import_product_titles').text('');
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            // Handle errors
                            var message = "An error occurred. Please try again";
                            if (textStatus === "timeout") {
                                message = "Request timed out. Please try again";
                            }
                            swal({
                                title: "Server Error",
                                text: message,
                                icon: "error",
                                button: true
                            }).then(function () {
                            // Reset UI elements after closing the message
                            $('#btn_import_product_titles_form')[0].reset();
                            $('#btn_import_close_product_titles, #excel_import_product_titles, #btn_import_product_titles, #btn_import_product_titles_template').prop('disabled', false).removeClass('disabled');
                            $('#message_import_product_titles').text('');
                            });
                        }
                    });
                });
        });
    </script>
    <script>
        $(document).ready(function() { // Set default tabs
            // Remove any stored tab state from the URL hash
            history.replaceState(null, null, ' ');

            // Clear localStorage or sessionStorage if used
            localStorage.removeItem('activeTab');
            sessionStorage.removeItem('activeTab');

            // Ensure the default tab is shown
            $('.nav-tabs a[href="#tab_default"]').tab('show');
        });

				function changeTab(href) {
    // Activate the target tab
    const targetTab = document.querySelector(href);
    if (targetTab) {
        // Remove 'active' and 'show' from all tabs
        document.querySelectorAll('.tab-pane').forEach(tab => tab.classList.remove('active', 'show'));
        // Add 'active' and 'show' to the selected tab
        targetTab.classList.add('active', 'show');
        
        // Update nav-link highlighting
        document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        const navLink = document.querySelector(`a[href="${href}"]`);
        if (navLink) {
            navLink.classList.add('active');
        }
    }
}
    </script>
</html>