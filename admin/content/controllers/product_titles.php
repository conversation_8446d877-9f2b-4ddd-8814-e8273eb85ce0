<?php include ("../../../db_conn.php");

$servername = DB_SERVER;
$username = DB_USERNAME;
$password = DB_PASSWORD;
$dbname = DB_NAME;
$curr_user = $_SESSION['auth_user']['user_name'];
$curr_date = date;
$Log_Page = 'Products';
$Log_Sub_Page = 'Content';
$Log_Sub_Page_1 = 'Product Titles';

// Create connection
$db = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);

// Clone data from other table
// $db->exec("INSERT IGNORE INTO gec_item_pricing (gecid, gecinv) SELECT gecid, gecinv FROM gec_item");

// =====================================================
// PERFORMANCE OPTIMIZATION: Enhanced Database Indexes
// =====================================================
// Execute comprehensive indexing strategy for optimal performance

// 1. PRIMARY TABLE INDEXES (gec_item_content)
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_content_gecid_primary ON gec_item_content (gecid)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_content_gecid_variation_type ON gec_item_content (gecid, variations_variation_type)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_content_item ON gec_item_content (item)");

// 2. CORE ITEM TABLE INDEXES (gec_item) - Multi-tenant optimized
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_gecid_optimized ON gec_item (gecid)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_client_prefix_gec_managed ON gec_item (client_prefix, GEC_Managed)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_gec_managed_client_prefix_status ON gec_item (GEC_Managed, client_prefix, item_status)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_item_optimized ON gec_item (item)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_status_type_filters ON gec_item (item_status, item_type, gec_alias_sku_type)");

// 3. RETAILER JOIN OPTIMIZATION INDEXES
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_homedepot_idm_omsid_optimized ON gec_retailer_homedepot_idm (OMSID)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_homedepot_idm_omsid_product_name ON gec_retailer_homedepot_idm (OMSID, Product_Name)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_targ_launchpad_tcin_optimized ON gec_retailer_targ_launchpad (TCIN)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_targ_launchpad_tcin_product_title ON gec_retailer_targ_launchpad (TCIN, Product_Title)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_walm_rl_item_wpid_optimized ON gec_retailer_walm_rl_item (WPID)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_retailer_walm_rl_item_wpid_product_name ON gec_retailer_walm_rl_item (WPID, Product_Name)");

// 4. JOIN FIELD OPTIMIZATION INDEXES
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_thd_omsid_optimized ON gec_item (thd_omsid)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_trgt_tcin_optimized ON gec_item (trgt_tcin)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_wmrl_wpid_optimized ON gec_item (wmrl_wpid)");

// 5. COMPOSITE INDEXES FOR JOIN CONDITIONS WITH CLIENT FILTERING
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_thd_omsid_client_prefix ON gec_item (thd_omsid, client_prefix)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_trgt_tcin_client_prefix ON gec_item (trgt_tcin, client_prefix)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_wmrl_wpid_client_prefix ON gec_item (wmrl_wpid, client_prefix)");

// 6. FILTERING AND SEARCH OPTIMIZATION
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_status_in_filter ON gec_item (item_status, client_prefix, GEC_Managed)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_type_alias_sku_filter ON gec_item (item_type, gec_alias_sku_type, client_prefix)");
$db->exec("CREATE INDEX IF NOT EXISTS idx_gec_item_content_variation_type_filter ON gec_item_content (variations_variation_type, gecid)");


/*
 * Example PHP implementation used for the index.html example
 */

// DataTables PHP library
include(base_app . 'vendor/datatables.net/editor-php/DataTables.php');

// Alias Editor classes so they are easy to use
use
	DataTables\Editor,
	DataTables\Editor\Field,
	DataTables\Editor\Format,
	DataTables\Editor\Mjoin,
	DataTables\Editor\Options,
	DataTables\Editor\Upload,
	DataTables\Editor\Validate,
	DataTables\Editor\ValidateOptions;

	// Handle status filter
	$clientFilter = ($_SESSION['global_clientPrefix'] == 'all_clients') ? '' : $_SESSION['global_clientPrefix'];
	$statusFilter = isset($_POST['statusFilter']) ? $_POST['statusFilter'] : '';
	$variationTypeFilter = isset($_POST['variationTypeFilter']) ? $_POST['variationTypeFilter'] : '';
	$gec_alias_sku_typeFilter = 'sku_wl';

	$select1 = isset($_POST['select1']) ? $_POST['select1'] : '';
	$instock = isset($_POST['instock']) ? $_POST['instock'] : '';

	$columns = [
		'pt_gec_single_product_title' => 'GEC Single Product Title',
		'pt_gec_variation_product_title' => 'GEC Variation Product Title',
		'pt_client_product_title' => 'Client Product Title',
		'pt_gec_home_depot_single_product_title' => 'GEC Home Depot Single Product Title',
		'pt_gec_home_depot_variation_product_title' => 'GEC Home Depot Variation Product Title',
		'pt_live_home_depot_product_title' => 'Live Home Depot Product Title',
		'pt_home_depot_product_title_status' => 'Home Depot Product Title Status',
		'pt_gec_bbb_overstock_single_product_title' => 'GEC BBB/Overstock Single Product Title',
		'pt_gec_bbb_overstock_variation_product_title' => 'GEC BBB/Overstock Variation Product Title',
		'pt_live_bbb_overstock_product_title' => 'Live BBB/Overstock Product Title',
		'pt_bbb_overstock_product_title_status' => 'BBB/Overstock Product Title Status',
		'pt_gec_target_single_product_title' => 'GEC Target Single Product Title',
		'pt_gec_target_variation_product_title' => 'GEC Target Variation Product Title',
		'pt_live_target_product_title' => 'Live Target Product Title',
		'pt_target_product_title_status' => 'Target Product Title Status',
		'pt_gec_walmart_single_product_title' => 'GEC Walmart Single Product Title',
		'pt_gec_walmart_variation_product_title' => 'GEC Walmart Variation Product Title',
		'pt_live_walmart_product_title' => 'Live Walmart Product Title',
		'pt_walmart_product_title_status' => 'Walmart Product Title Status',
		'pt_gec_amazon_single_product_title' => 'GEC Amazon Single Product Title',
		'pt_gec_amazon_variation_product_title' => 'GEC Amazon Variation Product Title',
		'pt_live_amazon_vc_product_title' => 'Live Amazon VC Product Title',
		'pt_amazon_vc_product_title_status' => 'Amazon VC Product Title Status',
		'pt_gec_wayfair_single_product_title' => 'GEC Wayfair Single Product Title',
		'pt_gec_wayfair_variation_product_title' => 'GEC Wayfair Variation Product Title',
		'pt_live_wayfair_product_title' => 'Live Wayfair Product Title',
		'pt_wayfair_product_title_status' => 'Wayfair Product Title Status',
		'pt_live_amazon_sc_product_title' => 'Live Amazon SC Product Title',
		'pt_amazon_sc_product_title_status' => 'Amazon SC Product Title Status',
		'pt_gec_houzz_single_product_title' => 'GEC Houzz Single Product Title',
		'pt_gec_houzz_variation_product_title' => 'GEC Houzz Variation Product Title',
		'pt_live_houzz_product_title' => 'Live Houzz Product Title',
		'pt_houzz_product_title_status' => 'Houzz Product Title Status',
		'pt_gec_home_depot_ca_single_product_title' => 'GEC Home Depot CA Single Product Title',
		'pt_gec_home_depot_ca_variation_product_title' => 'GEC Home Depot CA Variation Product Title',
		'pt_live_home_depot_ca_product_title' => 'Live Home Depot CA Product Title',
		'pt_home_depot_ca_product_title_status' => 'Home Depot CA Product Title Status'
	];

	Editor::inst($db, 'gec_item_content')
    ->fields(
        Field::inst('gec_item_content.gecid') ->xss(false), // GECID
        Field::inst('gec_item.item') ->xss(false), // GEC Item
		Field::inst('gec_item.thd_omsid') ->xss(false), // THD OMSID
		Field::inst('gec_item.thd_store_sku') ->xss(false), // THD Store SKU
		Field::inst('gec_item.bbb_ovsk_sku') ->xss(false), // OSTK SKU
		Field::inst('gec_item.bbb_ovsk_product_id') ->xss(false), // OSTK Product ID
		Field::inst('gec_item.trgt_tcin') ->xss(false), // Target TCIN
		Field::inst('gec_item.trgt_parent_tcin') ->xss(false), // Target Parent TCIN
		Field::inst('gec_item.trgt_dpci') ->xss(false), // Target DPCI
		Field::inst('gec_item.wmrl_wm_number') ->xss(false), // Walmart Item Number
		Field::inst('gec_item.amazon_asin') ->xss(false), // Amazon ASIN
		Field::inst('gec_item.wayf_sku') ->xss(false), // Wayfair SKU
		Field::inst('gec_item.houzz_product_id') ->xss(false), // Houzz Product ID
		Field::inst('gec_retailer_homedepot_idm.Product_Name') ->xss(false), // THD Product Title
		Field::inst('gec_retailer_targ_launchpad.Product_Title') ->xss(false), // Target Product Title
		Field::inst('gec_retailer_walm_rl_item.Product_Name') ->xss(false), // Walmart Product Title
				Field::inst('gec_item_content.pt_gec_single_product_title'),
				Field::inst('gec_item_content.pt_gec_variation_product_title'),
				Field::inst('gec_item_content.pt_client_product_title'),
				Field::inst('gec_item_content.pt_gec_home_depot_single_product_title'),
				Field::inst('gec_item_content.pt_gec_home_depot_variation_product_title'),
				Field::inst('gec_item_content.pt_live_home_depot_product_title'),
				Field::inst('gec_item_content.pt_home_depot_product_title_status'),
				Field::inst('gec_item_content.pt_gec_bbb_overstock_single_product_title'),
				Field::inst('gec_item_content.pt_gec_bbb_overstock_variation_product_title'),
				Field::inst('gec_item_content.pt_live_bbb_overstock_product_title'),
				Field::inst('gec_item_content.pt_bbb_overstock_product_title_status'),
				Field::inst('gec_item_content.pt_gec_target_single_product_title'),
				Field::inst('gec_item_content.pt_gec_target_variation_product_title'),
				Field::inst('gec_item_content.pt_live_target_product_title'),
				Field::inst('gec_item_content.pt_target_product_title_status'),
				Field::inst('gec_item_content.pt_gec_walmart_single_product_title'),
				Field::inst('gec_item_content.pt_gec_walmart_variation_product_title'),
				Field::inst('gec_item_content.pt_live_walmart_product_title'),
				Field::inst('gec_item_content.pt_walmart_product_title_status'),
				Field::inst('gec_item_content.pt_gec_amazon_single_product_title'),
				Field::inst('gec_item_content.pt_gec_amazon_variation_product_title'),
				Field::inst('gec_item_content.pt_live_amazon_vc_product_title'),
				Field::inst('gec_item_content.pt_amazon_vc_product_title_status'),
				Field::inst('gec_item_content.pt_gec_wayfair_single_product_title'),
				Field::inst('gec_item_content.pt_gec_wayfair_variation_product_title'),
				Field::inst('gec_item_content.pt_live_wayfair_product_title'),
				Field::inst('gec_item_content.pt_wayfair_product_title_status'),
				Field::inst('gec_item_content.pt_live_amazon_sc_product_title'),
				Field::inst('gec_item_content.pt_amazon_sc_product_title_status'),
				Field::inst('gec_item_content.pt_gec_houzz_single_product_title'),
				Field::inst('gec_item_content.pt_gec_houzz_variation_product_title'),
				Field::inst('gec_item_content.pt_live_houzz_product_title'),
				Field::inst('gec_item_content.pt_houzz_product_title_status'),
				Field::inst('gec_item_content.pt_gec_home_depot_ca_single_product_title'),
				Field::inst('gec_item_content.pt_gec_home_depot_ca_variation_product_title'),
				Field::inst('gec_item_content.pt_live_home_depot_ca_product_title'),
				Field::inst('gec_item_content.pt_home_depot_ca_product_title_status'),

				//variation
				Field::inst('gec_item_content.variations_variation_type'),

				//Filter used
				Field::inst('gec_item.item_status') ->xss(false),
				Field::inst('gec_item.prod_type') ->xss(false),
				Field::inst('gec_item.client_prefix') ->xss(false),
				Field::inst('gec_item.item_type') ->xss(false),
				Field::inst('gec_item.gec_alias_sku_type') ->xss(false),
				Field::inst('gec_item.GEC_Managed') ->xss(false)
	)
	
	->leftJoin('gec_item', 'gec_item_content.gecid', '=', 'gec_item.gecid')
	->leftJoin('gec_retailer_homedepot_idm', 'gec_item.thd_omsid', '=', 'gec_retailer_homedepot_idm.OMSID')
	->leftJoin('gec_retailer_targ_launchpad', 'gec_item.trgt_tcin', '=', 'gec_retailer_targ_launchpad.TCIN')
	->leftJoin('gec_retailer_walm_rl_item', 'gec_item.wmrl_wpid', '=', 'gec_retailer_walm_rl_item.WPID')
	->on('preEdit', function ($editor, $id, $values) use ($conn, $curr_user, $curr_date, $Log_Page, $Log_Sub_Page, $Log_Sub_Page_1, $columns) {
		foreach ($columns as $column => $title) {
			if (isset($values['gec_item_content'][$column])) {
				$editedValue = $values['gec_item_content'][$column];
				$get_client_prefix = substr($id, 0, 4);

				// Use prepared statement for security and performance
				$stmt = mysqli_prepare($conn, "INSERT INTO `gec_logs_audit`(`gecid`, `page`, `sub_page`, `sub_page_1`, `audit_prefix`, `edited_column`, `edited_value`, `edited_by`, `edited_datetime`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
				mysqli_stmt_bind_param($stmt, "sssssssss", $id, $Log_Page, $Log_Sub_Page, $Log_Sub_Page_1, $get_client_prefix, $title, $editedValue, $curr_user, $curr_date);
				mysqli_stmt_execute($stmt);
				mysqli_stmt_close($stmt);
			}
		}
	})

->where('gec_item.GEC_Managed', 'Yes')
->where(function ($q) use ($clientFilter, $statusFilter, $variationTypeFilter, $gec_alias_sku_typeFilter, $select1, $instock) {
					
	//LINK FILTER START

	if (!empty($select1) && $select1 === 'clientprovidedproducttitle') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_client_product_title', null, '=')
											->or_where('gec_item_content.pt_client_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gecsingleproducttitlemissing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gecvariationproducttitlemissing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_variation_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_variation_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_home_depot_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_home_depot_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_home_depot_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_bbb_overstock_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_bbb_overstock_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_bbb_overstock_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_target_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_target_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_target_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_walmart_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_walmart_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_walmart_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_amazon_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_amazon_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_amazon_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_wayfair_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_wayfair_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_wayfair_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_houzz_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_houzz_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_houzz_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	if (!empty($select1) && $select1 === 'gec_home_depot_ca_single_product_title_missing') {
		$q->where(function($query) {
								$query->where('gec_item_content.pt_gec_home_depot_ca_single_product_title', null, '=')
											->or_where('gec_item_content.pt_gec_home_depot_ca_single_product_title', '', '=');
						});
		$q->where_In('gec_item.prod_type', ['Product', 'Set']);
	}

	//LINK FILTER END

	if (!empty($clientFilter)) {
			$q->where('gec_item.client_prefix', '%' . trim($clientFilter, '"') . '%', 'LIKE');

			if (!empty($statusFilter)) {
					$statusFilter = trim($statusFilter, '"');
					if ($statusFilter == 'Null') {
							$q->whereNull('gec_item.item_status');
					} elseif ($statusFilter == 'ActivePDiscFuture') {
							$q->where_In('gec_item.item_status', ['Active', 'PDisc', 'Future']);
					} else {
							$q->where('gec_item.item_status', $statusFilter);
					}
			}

			if (!empty($gec_alias_sku_typeFilter)) {
				$trimmedFilter = trim($gec_alias_sku_typeFilter, '"');
				if ($trimmedFilter == 'sku_wl') {
						$q->where(function($query) {
								$query->where('gec_item.item_type', 'SKU')
											->or_where('gec_alias_sku_type', 'White Label');
						});
				} else {
						$q->where(function($query) use ($trimmedFilter) {
								$query->where('gec_item.item_type', $trimmedFilter)
											->or_where('gec_alias_sku_type', $trimmedFilter);
						});
				}
		}

		if (!empty($variationTypeFilter)) {
			$variationTypeFilter = trim($variationTypeFilter, '"');
			if ($variationTypeFilter == 'Null') {
					$q->whereNull('gec_item_content.variations_variation_type');
			} else {
				$q->where('gec_item_content.variations_variation_type', $variationTypeFilter);
			}
	}

	if (!empty($instock) && $instock === 'InStock') {
		$q->where('gec_item.combined_total_qty', 1, '>=');
	}
	
	} else {
			if (!empty($statusFilter)) {
					$statusFilter = trim($statusFilter, '"');
					if ($statusFilter == 'Null') {
							$q->whereNull('gec_item.item_status');
					} elseif ($statusFilter == 'ActivePDiscFuture') {
							$q->where_In('gec_item.item_status', ['Active', 'PDisc', 'Future']);
					} else {
							$q->where('gec_item.item_status', $statusFilter);
					}
			}

			if (!empty($gec_alias_sku_typeFilter)) {
				$trimmedFilter = trim($gec_alias_sku_typeFilter, '"');
				if ($trimmedFilter == 'sku_wl') {
						$q->where(function($query) {
								$query->where('gec_item.item_type', 'SKU')
											->or_where('gec_alias_sku_type', 'White Label');
						});
				} else {
						$q->where(function($query) use ($trimmedFilter) {
								$query->where('gec_item.item_type', $trimmedFilter)
											->or_where('gec_alias_sku_type', $trimmedFilter);
						});
				}
		}

		if (!empty($variationTypeFilter)) {
			$variationTypeFilter = trim($variationTypeFilter, '"');
			if ($variationTypeFilter == 'Null') {
					$q->whereNull('gec_item_content.variations_variation_type');
			} else {
				$q->where('gec_item_content.variations_variation_type', $variationTypeFilter);
			}
	}

	}
})	

->debug(false)
->pkey('gecid') // Set gecid as the primary key
->process($_POST)
->json();